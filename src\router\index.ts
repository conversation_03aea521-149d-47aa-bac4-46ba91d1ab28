import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
    {
        path: '/',
        name: 'Dashboard',
        component: () => import('../views/DashboardView.vue')
    },
    {
        path: '/notes',
        name: 'Notes',
        component: () => import('../views/NotesView.vue')
    },
    {
        path: '/books',
        name: 'Books',
        component: () => import('../views/BooksView.vue')
    },
    {
        path: '/folders',
        name: 'Folders',
        component: () => import('../views/FoldersView.vue')
    },
    {
        path: '/timer',
        name: 'Timer',
        component: () => import('../views/TimerView.vue')
    },
    {
        path: '/settings',
        name: 'Settings',
        component: () => import('../views/SettingsView.vue')
    }
]

const router = createRouter({
    history: createWebHashHistory(), // Use hash mode for Electron
    routes
})

export default router
