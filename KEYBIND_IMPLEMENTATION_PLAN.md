# Noti Application - Keybind Implementation Plan

This document provides a comprehensive, modular implementation plan for keyboard shortcuts in the Noti application. Each keybind is organized by category and priority, making it easy to implement incrementally and remove unwanted shortcuts.

## 📋 Implementation Overview

### Architecture
- **Global Keybind Handler**: Central system in `App.vue` or dedicated composable
- **View-Specific Handlers**: Individual handlers for each major view
- **Modal-Specific Handlers**: Dedicated handlers for modal interactions
- **Context-Aware System**: Different shortcuts based on current focus/context

### File Structure
```
src/
├── composables/
│   ├── useGlobalKeybinds.ts       # Global shortcuts
│   ├── useNotesKeybinds.ts        # Notes view shortcuts
│   ├── useBooksKeybinds.ts        # Books view shortcuts
│   ├── useFoldersKeybinds.ts      # Folders view shortcuts
│   ├── useTimerKeybinds.ts        # Timer view shortcuts
│   └── useModalKeybinds.ts        # Modal shortcuts
├── utils/
│   └── keybindUtils.ts            # Utility functions
└── types/
    └── keybinds.ts                # Type definitions
```

---

## 🚀 Phase 1: Essential Keybinds (High Priority)

### 1.1 Global Navigation
**File**: `src/composables/useGlobalKeybinds.ts`

```typescript
// Keybinds to implement:
const GLOBAL_KEYBINDS = {
  'ctrl+1': () => router.push('/'),           // Dashboard
  'ctrl+2': () => router.push('/notes'),      // Notes
  'ctrl+3': () => router.push('/books'),      // Books
  'ctrl+4': () => router.push('/folders'),    // Folders
  'ctrl+5': () => router.push('/timer'),      // Timer
  'ctrl+6': () => router.push('/settings'),   // Settings
  'ctrl+`': () => toggleSidebar(),            // Toggle sidebar
  'escape': () => closeActiveModal(),         // Close modals
  'f11': () => toggleFullscreen(),            // Fullscreen
}
```

**Implementation Steps**:
1. Create `useGlobalKeybinds.ts` composable
2. Add to `App.vue` setup function
3. Test each shortcut individually
4. **✅ Mark complete when working**

**Can Remove**: Any specific view shortcuts you don't use
**Dependencies**: Router, sidebar state management

---

### 1.2 Notes View - Core Actions
**File**: `src/composables/useNotesKeybinds.ts`

```typescript
// Essential notes shortcuts
const NOTES_CORE_KEYBINDS = {
  'ctrl+n': () => createNewNote(),             // New note
  'ctrl+s': () => saveCurrentNote(),           // Save note
  'ctrl+f': () => focusSearchBar(),            // Search
  'delete': () => deleteSelectedNotes(),       // Delete
  'ctrl+i': () => importNote(),                // Import
}
```

**Implementation Steps**:
1. Create composable with note-specific shortcuts
2. Integrate with existing NotesView functions
3. Test with current note editor
4. **✅ Mark complete when working**

**Can Remove**: Import shortcuts if not needed
**Dependencies**: Notes API, current note state

---

### 1.3 Rich Text Editor Shortcuts
**File**: `src/components/notes/NoteEditor.vue` (enhance existing)

```typescript
// Text formatting shortcuts (extend existing TipTap editor)
const EDITOR_KEYBINDS = {
  'ctrl+b': () => editor.chain().focus().toggleBold().run(),
  'ctrl+i': () => editor.chain().focus().toggleItalic().run(),
  'ctrl+u': () => editor.chain().focus().toggleUnderline().run(),
  'ctrl+shift+s': () => editor.chain().focus().toggleStrike().run(),
  'ctrl+k': () => showLinkModal(),
  'ctrl+z': () => editor.chain().focus().undo().run(),
  'ctrl+y': () => editor.chain().focus().redo().run(),
}
```

**Implementation Steps**:
1. Enhance existing NoteEditor keybind handler
2. Connect to TipTap editor commands
3. Test all formatting shortcuts
4. **✅ Mark complete when working**

**Can Remove**: Advanced formatting shortcuts you don't use
**Dependencies**: TipTap editor instance

---

### 1.4 Timer Controls
**File**: `src/composables/useTimerKeybinds.ts`

```typescript
// Timer control shortcuts
const TIMER_KEYBINDS = {
  'space': () => toggleTimer(),                // Start/pause
  'r': () => resetTimer(),                     // Reset
  's': () => skipTimer(),                      // Skip
  'digit1': () => switchToPomodoro(),          // Pomodoro mode
  'digit2': () => switchToShortBreak(),        // Short break
  'digit3': () => switchToLongBreak(),         // Long break
}
```

**Implementation Steps**:
1. Create timer-specific composable
2. Connect to existing timer store actions
3. Only activate when TimerView is focused
4. **✅ Mark complete when working**

**Can Remove**: Number shortcuts if you prefer buttons only
**Dependencies**: Timer store, view focus detection

---

## 🔧 Phase 2: Quality of Life Keybinds (Medium Priority)

### 2.1 Multi-Selection and Navigation
**File**: `src/composables/useNotesKeybinds.ts` (extend)

```typescript
// Advanced selection shortcuts
const NOTES_SELECTION_KEYBINDS = {
  'ctrl+a': () => selectAllNotes(),            // Select all
  'ctrl+click': (event) => toggleNoteSelection(event), // Multi-select
  'shift+click': (event) => rangeSelectNotes(event),   // Range select
  'arrowup': () => navigateNoteList('up'),     // Navigate up
  'arrowdown': () => navigateNoteList('down'), // Navigate down
  'enter': () => openSelectedNote(),           // Open note
}
```

**Implementation Steps**:
1. Extend notes composable with selection logic
2. Implement note list navigation
3. Connect to existing multi-select system
4. **✅ Mark complete when working**

**Can Remove**: Arrow navigation if you prefer mouse-only
**Dependencies**: Note selection state, filtered notes list

---

### 2.2 Books View Shortcuts
**File**: `src/composables/useBooksKeybinds.ts`

```typescript
// Books management shortcuts
const BOOKS_KEYBINDS = {
  'ctrl+n': () => openAddBookModal(),          // Add book
  'ctrl+f': () => focusBookSearch(),           // Search books
  'enter': () => openBookDetails(),            // Open details
  'delete': () => deleteSelectedBook(),        // Delete book
  'ctrl+o': () => openBookNote(),              // Open note
  'ctrl+shift+n': () => createBookNote(),      // Create note
}
```

**Implementation Steps**:
1. Create books-specific keybind composable
2. Connect to existing BooksView functions
3. Test with book selection and modals
4. **✅ Mark complete when working**

**Can Remove**: Note creation shortcuts if not used
**Dependencies**: Books API, book selection state

---

### 2.3 Folders View Navigation
**File**: `src/composables/useFoldersKeybinds.ts`

```typescript
// Folder navigation shortcuts
const FOLDERS_KEYBINDS = {
  'ctrl+n': () => createNewFolder(),           // New folder
  'f2': () => renameSelectedFolder(),          // Rename
  'delete': () => deleteSelectedFolders(),     // Delete
  'enter': () => enterSelectedFolder(),        // Enter folder
  'backspace': () => navigateUp(),             // Go up
  'ctrl+home': () => goToRoot(),               // Go to root
}
```

**Implementation Steps**:
1. Create folders-specific composable
2. Connect to folder navigation logic
3. Test folder hierarchy navigation
4. **✅ Mark complete when working**

**Can Remove**: Navigation shortcuts if not needed
**Dependencies**: Folder navigation state, hierarchy system

---

### 2.4 Modal Controls
**File**: `src/composables/useModalKeybinds.ts`

```typescript
// Universal modal shortcuts
const MODAL_KEYBINDS = {
  'enter': () => confirmModal(),               // Confirm action
  'escape': () => cancelModal(),               // Cancel/close
  'tab': () => navigateModalFields('forward'), // Next field
  'shift+tab': () => navigateModalFields('backward'), // Previous field
}
```

**Implementation Steps**:
1. Create modal composable for universal shortcuts
2. Apply to all modal components
3. Test with different modal types
4. **✅ Mark complete when working**

**Can Remove**: Tab navigation if not needed
**Dependencies**: Modal state management, form field detection

---

## ⚡ Phase 3: Advanced Features (Low Priority)

### 3.1 Advanced Search and Commands
**File**: `src/composables/useAdvancedKeybinds.ts`

```typescript
// Advanced shortcuts for power users
const ADVANCED_KEYBINDS = {
  'ctrl+/': () => showKeybindHelp(),           // Help
}
```

**Implementation Steps**:
1. Create advanced features composable
2. Create keybind help modal
3. **✅ Mark complete when working**

**Can Remove**: Help system if not needed
**Dependencies**: New components for help system

---

## 🔍 Search & Filter (Simplified)

### Text Editing (Universal)
```typescript
const TEXT_EDITING_KEYBINDS = {
  'ctrl+a': () => selectAll(),                 // Select all
  'ctrl+c': () => copy(),                      // Copy
  'ctrl+x': () => cut(),                       // Cut
  'ctrl+v': () => paste(),                     // Paste
  'ctrl+z': () => undo(),                      // Undo
  'ctrl+y': () => redo(),                      // Redo
  'ctrl+f': () => findInText(),                // Find in text
}
```

---

## 🛠️ Implementation Utilities

### Core Utility Functions
**File**: `src/utils/keybindUtils.ts`

```typescript
// Utility functions for keybind system
export interface KeybindConfig {
  key: string
  handler: () => void
  description: string
  category: string
  priority: 'high' | 'medium' | 'low'
  enabled: boolean
}

export class KeybindManager {
  private keybinds: Map<string, KeybindConfig> = new Map()
  
  register(config: KeybindConfig) { /* ... */ }
  unregister(key: string) { /* ... */ }
  handleKeyEvent(event: KeyboardEvent) { /* ... */ }
  getActiveKeybinds() { /* ... */ }
  toggleKeybind(key: string) { /* ... */ }
}
```

### Type Definitions
**File**: `src/types/keybinds.ts`

```typescript
export interface KeybindContext {
  view: 'dashboard' | 'notes' | 'books' | 'folders' | 'timer' | 'settings'
  modalOpen: boolean
  editorFocused: boolean
  selectedItems: any[]
}

export type KeybindHandler = (context: KeybindContext) => void
```

---

## 📝 Implementation Checklist

### Phase 1 - Essential (Week 1)
- [ ] Global navigation (Ctrl + 1-6)
- [ ] Notes core actions (Ctrl + N/S/F)
- [ ] Rich text editor shortcuts
- [ ] Timer controls (Space, R, S)
- [ ] Modal escape handling

### Phase 2 - Quality of Life (Week 2)
- [ ] Multi-selection in notes
- [ ] Books view shortcuts
- [ ] Folders navigation
- [ ] Enhanced modal controls
- [ ] Search improvements

### Phase 3 - Advanced (Week 3)
- [ ] Help system
- [ ] Keybind documentation

### Testing Checklist
- [ ] All shortcuts work in correct contexts
- [ ] No conflicts between shortcuts
- [ ] Shortcuts disabled when inappropriate
- [ ] Modal shortcuts work universally
- [ ] Editor shortcuts don't interfere with typing

---

## 🗑️ Easy Removal Guide

To remove specific keybinds you don't want:

1. **Individual Shortcuts**: Comment out or delete specific lines in the keybind objects
2. **Entire Categories**: Comment out entire sections in the composables
3. **Complete Features**: Don't import/use specific composables
4. **Disable Temporarily**: Use the `enabled: false` flag in KeybindConfig

### Example Removal:
```typescript
// To remove specific shortcuts, comment out:
// 'ctrl+e': () => exportCurrentNote(),

// To disable an entire category:
// const BACKUP_KEYBINDS = { /* ... */ }  // Comment out entire object
```

---

## 🧪 Testing Strategy

1. **Unit Tests**: Test individual keybind handlers
2. **Integration Tests**: Test keybinds within views
3. **Manual Testing**: Verify shortcuts work as expected
4. **Conflict Testing**: Ensure no overlapping shortcuts
5. **Context Testing**: Verify shortcuts only work in correct contexts

---

## 📋 Notes

- Start with Phase 1 for essential functionality
- Each phase can be implemented independently
- Remove any shortcuts that don't fit your workflow
- Test thoroughly before moving to next phase
- Consider user preferences/settings for customization

This modular approach allows you to implement exactly what you need and easily remove what you don't want.