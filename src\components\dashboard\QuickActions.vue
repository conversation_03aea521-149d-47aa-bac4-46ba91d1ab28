<template>
  <div class="quick-actions">
    <h2 class="section-title">Quick Actions</h2>
    <div class="actions-grid">
      <!-- New Note -->
      <button class="action-card notes-icon" @click="createNewNote">
        <div class="action-icon">
          <img src="/icons/notes-icon.svg" alt="New Note" width="24" height="24" />
        </div>
        <div class="action-content">
          <div class="action-title">New Note</div>
          <div class="action-description">Create a new note</div>
        </div>
      </button>

      <!-- Add Book -->
      <button class="action-card books-icon" @click="addNewBook">
        <div class="action-icon">
          <img src="/icons/books-icon.svg" alt="Add Book" width="24" height="24" />
        </div>
        <div class="action-content">
          <div class="action-title">Add Book</div>
          <div class="action-description">Add a new book to your collection</div>
        </div>
      </button>

      <!-- Start Timer -->
      <button class="action-card timer-icon" @click="startTimer">
        <div class="action-icon">
          <img src="/icons/timer-icon.svg" alt="Start Timer" width="24" height="24" />
        </div>
        <div class="action-content">
          <div class="action-title">Start Timer</div>
          <div class="action-description">Begin a focus session</div>
        </div>
      </button>

      <!-- Create Folder -->
      <button class="action-card folders-icon" @click="createNewFolder">
        <div class="action-icon">
          <img src="/icons/folders-icon.svg" alt="New Folder" width="24" height="24" />
        </div>
        <div class="action-content">
          <div class="action-title">New Folder</div>
          <div class="action-description">Organize your notes</div>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useTimerStore } from '../../stores/timerStore'

const router = useRouter()
const timerStore = useTimerStore()

const createNewNote = () => {
  router.push('/notes')
  // The notes view will handle creating a new note
}

const addNewBook = () => {
  router.push('/books')
  // The books view will handle adding a new book
}

const startTimer = () => {
  router.push('/timer')
  // The timer view will handle starting a new session
}

const createNewFolder = () => {
  router.push('/folders')
  // The folders view will handle creating a new folder
}
</script>

<style scoped>
.quick-actions {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--color-text-primary);
  letter-spacing: -0.025em;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
}

.action-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  background-color: var(--color-card-hover-bg);
  transform: translateY(-1px);
  border-color: var(--color-border-hover);
}

.action-card:active {
  transform: translateY(0);
}

.action-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background-color: var(--color-dashboard-action-icon-bg);
  border: 1px solid var(--color-dashboard-action-icon-border);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-card:hover .action-icon {
  background-color: var(--color-bg-tertiary);
  border-color: var(--color-border-hover);
}

.action-icon img {
  width: 24px;
  height: 24px;
  filter: var(--icon-filter);
  transition: filter 0.2s ease;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.action-description {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 1024px) {
  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .quick-actions {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 1.1rem;
    margin-bottom: 16px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .action-card {
    padding: 16px;
    gap: 12px;
    border-radius: 10px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
  }

  .action-icon img {
    width: 20px;
    height: 20px;
  }

  .action-title {
    font-size: 0.9rem;
    margin-bottom: 2px;
  }

  .action-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .action-card {
    padding: 14px;
    gap: 10px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
  }

  .action-icon img {
    width: 18px;
    height: 18px;
  }
}
</style>
