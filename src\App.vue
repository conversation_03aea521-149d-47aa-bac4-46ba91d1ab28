<template>
  <div class="app-container" :class="themeClass">
    <TitleBar />
    <div class="main-content">
      <SidebarNavigation />
      <div class="content-container">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onBeforeUnmount, computed } from 'vue'
import SidebarNavigation from './components/common/SidebarNavigation.vue'
import TitleBar from './components/common/TitleBar.vue'
import { useTimerStore } from './stores/timerStore'
import { useSettingsStore } from './stores/settingsStore'
import { resolveTheme } from './utils/themeUtils'
import { useGlobalKeybinds } from './composables/useGlobalKeybinds'

export default defineComponent({
  name: 'App',
  components: {
    SidebarNavigation,
    TitleBar
  },
  setup() {
    const timerStore = useTimerStore()
    const settingsStore = useSettingsStore()
    const { setup: setupKeybinds, cleanup: cleanupKeybinds } = useGlobalKeybinds()

    // Computed theme class for the app container
    const themeClass = computed(() => {
      const resolvedTheme = resolveTheme(settingsStore.currentTheme)
      return `theme-${resolvedTheme}`
    })

    // Initialize stores when app mounts
    onMounted(async () => {
      try {
        // Initialize settings first (this will apply the theme)
        await settingsStore.initialize()

        // Initialize timer store (loads active session and settings)
        await timerStore.initialize()

        // Initialize global keybinds system
        setupKeybinds()

        console.log('App initialized with Pinia stores and keybinds')
      } catch (error) {
        console.error('Failed to initialize app stores:', error)
      }
    })

    // Cleanup when app unmounts
    onBeforeUnmount(() => {
      try {
        // Clean up keybinds system
        cleanupKeybinds()

        // Clean up timer intervals and save state
        timerStore.cleanup()

        // Clean up settings store (theme watchers)
        settingsStore.cleanup()

        console.log('App cleanup completed')
      } catch (error) {
        console.error('Error during app cleanup:', error)
      }
    })

    return {
      timerStore,
      settingsStore,
      themeClass
    }
  }
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  height: 100vh;
  overflow: hidden;
  /* Prevent text selection by default throughout the app */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.content-container {
  flex: 1;
  overflow: auto;
  padding: 0px;
  background-color: var(--color-bg-primary);
  position: relative;  /* Add this line */
  z-index: 1;         /* Add this line */
}

/* Ensure all interactive elements are non-draggable */
button, a, input, textarea, select, [role="button"] {
  -webkit-app-region: no-drag;
}

/* Reset any potential imported defaults from style.css */
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  max-width: none;
  text-align: left;
}

/* Class to enable text selection for editable elements */
.user-select-text, input, textarea {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Style rules for modal components */
.modal-header, .modal-footer {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Allow text selection in text fields and selectable content */
.modal-content .selectable-text,
.modal-content input,
.modal-content textarea {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
</style>