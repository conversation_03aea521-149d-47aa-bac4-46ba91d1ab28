// Folders API - Specialized functions for folder operations
import {
    createFolder,
    getAllFold<PERSON>,
    getFolderById,
    getChildFold<PERSON> as getChildren,
    updateFolder,
    deleteFolder as deleteFolderFromDb, // Aliased original deleteFolder
    getNotesByFolderId,
    updateNote,
    Folder,
    Note,
    getAllNotes,
    FolderWithNoteCount,
    getAllFoldersWithNoteCounts,
    getFolderByBookId, // Added static import to replace dynamic import
    getInheritedBookId // Added for book_id inheritance
} from '../database/database-api';
import { sanitizeFilename } from '../../utils/filename-sanitizer';
import { notifyFolderChange } from '../database/database-hooks';

// Enhanced folder with additional properties
export interface FolderWithMeta extends Folder {
    notesCount?: number;
    childFoldersCount?: number;
    children?: FolderWithMeta[];
}

// Helper function to validate a folder ID
const validateFolderId = (id: number): boolean => {
    return id !== 0 && Number.isInteger(id) && id > 0;
};

// Helper function to validate a folder object
const validateFolder = (folder: Partial<Folder>): string[] => { // Allow partial for updates
    const errors: string[] = [];

    // Name validation (required for create, optional but validated if present for update)
    if (folder.name !== undefined) {
        if (typeof folder.name !== 'string' || folder.name.trim() === '') {
            errors.push('Folder name must be a non-empty string');
        }
    }

    // Parent ID validation (optional but validated if present)
    if (folder.parent_id !== undefined && folder.parent_id !== null && typeof folder.parent_id !== 'number') {
        errors.push('Parent folder ID must be a number or null');
    }

    // Validate folder ID if present
    if (folder.id !== undefined && !validateFolderId(folder.id)) {
        errors.push('Folder ID must be a positive integer greater than 0');
    }

    return errors;
};

// Helper function to get all descendant folder IDs of a given folder
async function getFolderDescendantsIds(folderId: number, allFoldersGetter: () => Promise<Folder[]>): Promise<number[]> {
    const descendants: number[] = [];
    const allFolders = await allFoldersGetter(); // Use a getter to fetch all folders once if needed by caller

    function findChildrenRecursive(currentFolderId: number) {
        const children = allFolders.filter(f => f.parent_id === currentFolderId);
        for (const child of children) {
            if (child.id !== undefined) {
                descendants.push(child.id);
                findChildrenRecursive(child.id);
            }
        }
    }

    findChildrenRecursive(folderId);
    return descendants;
}

// Create a new folder with validation and book_id inheritance
export const createFolderWithValidation = async (folder: Folder & { skipBackupEvent?: boolean }): Promise<Folder> => {
    // Validate folder - ensure name is present for creation
    if (!folder.name || typeof folder.name !== 'string' || folder.name.trim() === '') {
        throw new Error('Folder name is required for creation');
    }
    const validationErrors = validateFolder(folder);
    if (validationErrors.length > 0) {
        throw new Error(`Invalid folder data: ${validationErrors.join(', ')}`);
    }

    try {
        // If book_id is not explicitly set and we have a parent, inherit book_id from parent hierarchy
        let folderToCreate = { ...folder };
        if ((folderToCreate.book_id === null || folderToCreate.book_id === undefined) && folderToCreate.parent_id) {
            const inheritedBookId = await getInheritedBookId(folderToCreate.parent_id);
            if (inheritedBookId !== null) {
                folderToCreate.book_id = inheritedBookId;
                console.log(`Inheriting book_id ${inheritedBookId} for new folder "${folderToCreate.name}" from parent hierarchy`);
            }
        }

        const result = await createFolder(folderToCreate);

        // Notify database hooks about folder creation (unless explicitly skipped)
        if (result.id && !folder.skipBackupEvent) {
            notifyFolderChange('create', result.id, {
                name: result.name,
                parent_id: result.parent_id,
                book_id: result.book_id
            });
        }

        return result;
    } catch (error: any) {
        console.error('Error in createFolderWithValidation:', error);
        throw new Error(`Failed to create folder: ${error.message}`);
    }
};

// Update a folder with validation
export const updateFolderWithValidation = async (id: number, folderUpdates: Partial<Folder>): Promise<Folder> => {
    // Ensure id is a number
    if (typeof id !== 'number') {
        throw new Error('Folder ID is required and must be a number');
    }

    const folderToUpdate = await getFolderById(id);
    if (!folderToUpdate) {
        throw new Error(`Folder with ID ${id} not found.`);
    }

    // Protection for "Books" root folder: cannot be renamed.
    if (folderToUpdate.name === 'Books' && folderToUpdate.parent_id === null && folderUpdates.name !== undefined && folderUpdates.name !== 'Books') {
        throw new Error('The \"Books\" root folder cannot be renamed.');
    }

    // Protection for "Books" root folder: parent_id cannot be changed.
    if (folderToUpdate.name === 'Books' && folderToUpdate.parent_id === null && folderUpdates.parent_id !== undefined && folderUpdates.parent_id !== null) {
        throw new Error('The \"Books\" root folder cannot be moved.');
    }

    // Validate only the fields provided in the update
    const validationErrors = validateFolder(folderUpdates);
    if (validationErrors.length > 0) {
        throw new Error(`Invalid folder update data: ${validationErrors.join(', ')}`);
    }

    // Check for circular references
    if (folderUpdates.parent_id !== undefined && folderUpdates.parent_id !== null) {
        if (folderUpdates.parent_id === id) {
            throw new Error('A folder cannot be its own parent');
        }

        // Optimized Ancestry Check: Traverse upwards from the new parent
        let currentAncestorId = folderUpdates.parent_id;
        const allFolders = await getAllFolders(); // Fetch all folders once for this check
        const visited = new Set<number>(); // To prevent infinite loops in case of data corruption

        while (currentAncestorId !== null && currentAncestorId !== undefined) {
            if (visited.has(currentAncestorId)) {
                console.error("Circular reference detected during ancestry check - data might be corrupted.");
                throw new Error('Data integrity issue: circular reference detected.');
            }
            visited.add(currentAncestorId);

            if (currentAncestorId === id) {
                throw new Error('Cannot move a folder into one of its own descendants.');
            }
            const parentFolder = allFolders.find(f => f.id === currentAncestorId);
            if (!parentFolder) break; // Should not happen if data is consistent
            currentAncestorId = parentFolder.parent_id;
        }
    }

    try {
        const result = await updateFolder(id, folderUpdates);

        // Notify database hooks about folder update
        notifyFolderChange('update', id, {
            name: result.name,
            parent_id: result.parent_id,
            book_id: result.book_id,
            updatedFields: Object.keys(folderUpdates)
        });

        return result;
    } catch (error: any) {
        console.error('Error in updateFolderWithValidation:', error);
        throw new Error(`Failed to update folder ${id}: ${error.message}`);
    }
};

// Get folder with notes count
export const getFolderWithNotesCount = async (id: number): Promise<FolderWithMeta> => {
    if (!validateFolderId(id)) {
        throw new Error(`Invalid folder ID: ${id}`);
    }

    try {
        const folder = await getFolderById(id);
        if (!folder) {
            throw new Error(`Folder with ID ${id} not found`);
        }

        const notes: Note[] = await getNotesByFolderId(id);
        const childFolders: Folder[] = await getChildren(id);

        return {
            ...folder,
            notesCount: notes.length,
            childFoldersCount: childFolders.length
        };
    } catch (error: any) {
        console.error('Error in getFolderWithNotesCount:', error);
        throw new Error(`Failed to get folder details for ID ${id}: ${error.message}`);
    }
};

// Get folder hierarchy (for tree view)
export const getFolderHierarchy = async (): Promise<FolderWithMeta[]> => {
    try {
        console.log('Getting folder hierarchy with DB note counts...');
        // Use the new function to get folders with pre-calculated note counts
        const allFoldersWithCounts: FolderWithNoteCount[] = await getAllFoldersWithNoteCounts();
        console.log(`Found ${allFoldersWithCounts.length} folders total with note counts.`);

        if (!allFoldersWithCounts || !Array.isArray(allFoldersWithCounts)) {
            console.error('Invalid folders data returned from database');
            return [];
        }

        const folderMap: Map<number, FolderWithMeta> = new Map();

        // Initialize map and add children array, using pre-fetched notes counts
        for (const folder of allFoldersWithCounts) {
            if (folder && typeof folder.id === 'number') {
                // notesCount is now directly available from the folder object
                folderMap.set(folder.id, { ...folder, children: [], childFoldersCount: 0 }); // Initialize childFoldersCount
            }
        }

        console.log(`Initialized folder map with ${folderMap.size} entries`);
        const rootFolders: FolderWithMeta[] = [];
        let rootCount = 0;
        let childCount = 0;
        let orphanCount = 0;

        allFoldersWithCounts.forEach(folder => {
            // Skip invalid folders
            if (!folder || typeof folder.id !== 'number') {
                console.warn('Skipping invalid folder in hierarchy building');
                return;
            }

            const currentFolder = folderMap.get(folder.id);
            if (!currentFolder) {
                console.warn(`Folder ${folder.id} not found in folderMap`);
                return;
            }

            if (folder.parent_id === null || folder.parent_id === undefined) {
                rootFolders.push(currentFolder);
                rootCount++;
            } else {
                const parentFolder = folderMap.get(folder.parent_id);
                if (parentFolder && parentFolder.children) {
                    parentFolder.children.push(currentFolder);
                    childCount++;
                } else {
                    console.warn(`Orphaned folder found: ID ${folder.id}, Name: ${folder.name}`);
                    // Add orphans to root to ensure they are not lost, though this indicates a data issue.
                    rootFolders.push(currentFolder);
                    orphanCount++;
                }
            }
        });

        console.log(`Hierarchy built: ${rootCount} root folders, ${childCount} child folders, ${orphanCount} orphaned folders`);

        // Populate childFoldersCount for all folders and adjust notesCount for "Books" root folder
        // Convert Map.values() iterator to an array to avoid downlevelIteration issues
        const allFoldersInMap = Array.from(folderMap.values());

        for (const folder of allFoldersInMap) {
            // Set/Update childFoldersCount based on the constructed children array
            folder.childFoldersCount = folder.children ? folder.children.length : 0;

            // Specifically for the "Books" root folder, override notesCount
            if (folder.name === 'Books' && folder.parent_id === null) {
                // The "item count" (notesCount) for "Books" folder should be the number of actual book sub-folders
                folder.notesCount = folder.childFoldersCount; // which is folder.children.length
            }
            // For all other folders, notesCount remains as fetched from getAllFoldersWithNoteCounts (spread into folderMap)
            // and childFoldersCount is now correctly set.
        }

        // Optional: Sort root folders and children alphabetically by name
        const sortFolders = (folders: FolderWithMeta[]) => {
            if (!Array.isArray(folders)) {
                console.warn('Cannot sort non-array folders');
                return;
            }

            folders.sort((a, b) => {
                if (!a || !b || typeof a.name !== 'string' || typeof b.name !== 'string') {
                    console.warn('Invalid folder objects found during sort');
                    return 0;
                }
                return a.name.localeCompare(b.name);
            });

            folders.forEach(folder => {
                if (folder && folder.children && Array.isArray(folder.children) && folder.children.length > 0) {
                    sortFolders(folder.children);
                }
            });
        };

        try {
            sortFolders(rootFolders);
        } catch (sortError) {
            console.error('Error sorting folders:', sortError);
        }

        console.log(`Returning hierarchy with ${rootFolders.length} root folders`);
        return rootFolders;
    } catch (error: any) {
        console.error('Error in getFolderHierarchy:', error);
        throw new Error(`Failed to get folder hierarchy: ${error.message}`);
    }
};

// Delete folder and optionally move notes to another folder
export const deleteFolderAndHandleNotes = async (id: number, targetFolderId: number | null = null): Promise<{ success: boolean; id: number }> => {
    if (typeof id !== 'number') {
        throw new Error('Folder ID must be a number');
    }
    if (targetFolderId !== null && typeof targetFolderId !== 'number') {
        throw new Error('Target Folder ID must be a number or null');
    }
    if (id === targetFolderId) {
        throw new Error('Target folder cannot be the same as the folder being deleted');
    }

    const folderToDelete = await getFolderById(id);
    if (!folderToDelete) {
        throw new Error(`Folder with ID ${id} not found.`);
    }

    // Protection for "Books" root folder: cannot be deleted.
    if (folderToDelete.name === 'Books' && folderToDelete.parent_id === null) {
        throw new Error('The \"Books\" root folder cannot be deleted.');
    }

    // Protection for individual book folders: cannot be deleted directly.
    // They are handled by the book deletion process (moved to root).
    if (folderToDelete.book_id !== null && folderToDelete.book_id !== undefined) {
        throw new Error('Book-specific folders cannot be deleted directly. Delete the book instead to move its folder to the root.');
    }

    // Legacy protection for old book folders that don't have book_id set yet
    const booksRootFolder = await ensureBooksRootFolder(); // Get the main "Books" folder
    if (folderToDelete.parent_id === booksRootFolder.id && folderToDelete.name.startsWith('Book_')) {
        throw new Error('Book-specific folders cannot be deleted directly. Delete the book instead to move its folder to the root.');
    }

    try {
        // Calculate backup path for the folder before deletion
        const folderBackupPath = await calculateFolderBackupPath(folderToDelete);
        const folderHierarchyPath = await calculateFolderHierarchyPath(folderToDelete);

        if (targetFolderId !== null) {
            // Check if target folder exists
            const targetFolderExists = await getFolderById(targetFolderId);
            if (!targetFolderExists) {
                throw new Error(`Target folder with ID ${targetFolderId} not found`);
            }

            const notesToMove: Note[] = await getNotesByFolderId(id);

            // Use updateNote from database-api, not updateFolder
            const updatePromises = notesToMove.map(note =>
                updateNote(note.id!, { folder_id: targetFolderId })
            );
            await Promise.all(updatePromises);
        }

        // First delete the folder (database constraints handle children/notes based on schema)
        const result = await deleteFolderFromDb(id);

        // Notify database hooks about folder deletion
        notifyFolderChange('delete', id, {
            name: folderToDelete.name,
            parent_id: folderToDelete.parent_id,
            book_id: folderToDelete.book_id
        });

        return result;
    } catch (error: any) {
        console.error('Error in deleteFolderAndHandleNotes:', error);
        // Provide more specific error messages
        if (error.message.includes('FOREIGN KEY constraint failed')) {
            throw new Error(`Cannot delete folder ${id} due to database constraints. Ensure child items are handled.`);
        }
        throw new Error(`Failed to delete folder ${id}: ${error.message}`);
    }
};

// Function to get the Books root folder (should always exist after DB initialization)
export const ensureBooksRootFolder = async (): Promise<Folder> => {
  try {
    // Get all root folders
    const rootFolders = await getChildren(null);

    // Find the Books folder (should always exist since it's created during DB init)
    const booksFolder = rootFolders.find(folder => folder.name === 'Books');

    if (booksFolder) {
      console.log('Books root folder found with ID:', booksFolder.id);
      return booksFolder;
    }

    // Fallback: If for some reason the Books folder doesn't exist, create it
    // This should rarely happen since it's created during database initialization
    console.warn('Books folder not found, creating it as fallback...');

    const newBooksFolder: Folder = {
      name: 'Books',
      parent_id: null,
      color: '#4285F4', // Blue color for the Books folder
    };

    const createdFolder = await createFolderWithValidation(newBooksFolder);
    console.log('Created Books root folder with ID:', createdFolder.id);
    return createdFolder;
  } catch (error) {
    console.error('Error ensuring Books root folder exists:', error);
    throw new Error(`Failed to ensure Books root folder: ${error.message}`);
  }
};

// Function to get the book folder for a specific book
export const getBookFolder = async (bookId: number): Promise<Folder | null> => {
  try {
    // First try to find folder by book_id (new method)
    let bookFolder = await getFolderByBookId(bookId);

    if (bookFolder) {
      console.log(`Found folder for book ID ${bookId} using book_id field: ${bookFolder.name}`);
      return bookFolder;
    }

    // Fallback to old method for backward compatibility
    console.log(`No folder found with book_id=${bookId}, trying legacy name-based lookup...`);

    // Get the Books root folder
    const booksRootFolder = await ensureBooksRootFolder();

    // Get all direct children of the Books folder
    const bookFolders = await getChildren(booksRootFolder.id);

    // Find the folder that matches the book ID in the name (legacy method)
    bookFolder = bookFolders.find(folder => folder.name.startsWith(`Book_${bookId}_`));

    if (bookFolder) {
      console.log(`Found folder for book ID ${bookId} using legacy name pattern: ${bookFolder.name}`);
      // Migrate this folder to use the new book_id field
      try {
        await updateFolderWithValidation(bookFolder.id, { book_id: bookId });
        console.log(`Migrated folder ${bookFolder.id} to use book_id field`);
      } catch (migrationError) {
        console.warn(`Failed to migrate folder ${bookFolder.id} to use book_id:`, migrationError);
      }
      return bookFolder;
    }

    console.warn(`No folder found for book ID ${bookId} with either method.`);
    return null;
  } catch (error) {
    console.error(`Error getting folder for book ID ${bookId}:`, error);
    return null;
  }
};

/**
 * Calculate the backup path where a folder would be stored
 * @param folder - The folder to calculate path for
 * @returns Promise<string> - The backup path
 */
const calculateFolderBackupPath = async (folder: Folder): Promise<string> => {
    try {
        const folderHierarchyPath = await calculateFolderHierarchyPath(folder);
        return folderHierarchyPath || sanitizeFilename(folder.name);
    } catch (error) {
        console.warn('Error calculating folder backup path:', error);
        return sanitizeFilename(folder.name);
    }
};

/**
 * Calculate the folder hierarchy path
 * @param folder - The folder to calculate path for
 * @returns Promise<string> - The folder hierarchy path
 */
const calculateFolderHierarchyPath = async (folder: Folder): Promise<string> => {
    try {
        const pathParts: string[] = [];
        let currentFolder = folder;

        while (currentFolder) {
            pathParts.unshift(sanitizeFilename(currentFolder.name));
            
            if (currentFolder.parent_id) {
                currentFolder = await getFolderById(currentFolder.parent_id);
                if (!currentFolder) break; // Safety check
            } else {
                break;
            }
        }

        return pathParts.join('/');
    } catch (error) {
        console.warn('Error calculating folder hierarchy path:', error);
        return sanitizeFilename(folder.name);
    }
};

// Export default functions that will be used by IPC handlers
export default {
    createFolder: createFolderWithValidation,
    getAllFolders,
    getFolderById: getFolderWithNotesCount,
    getChildren,
    getFolderHierarchy,
    updateFolder: updateFolderWithValidation, // Already updated
    deleteFolder: deleteFolderAndHandleNotes, // Already updated
    ensureBooksRootFolder,
    getBookFolder,
    getInheritedBookId, // Added to export to replace dynamic import
};