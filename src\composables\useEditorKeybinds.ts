// Rich text editor keybinds composable
import { ref, onMounted, onBeforeUnmount } from 'vue'
import type { Editor } from '@tiptap/core'
import { globalKeybindManager } from '../utils/keybindUtils'
import type { KeybindContext } from '../types/keybinds'
import { KeybindCategory } from '../types/keybinds'

export function useEditorKeybinds() {
  const editor = ref<Editor | null>(null)
  const isActive = ref(false)

  // Editor action functions
  let showLinkModal: () => void = () => console.log('🔗 Show link modal')
  let showFontModal: () => void = () => console.log('🔤 Show font modal')
  let showColorModal: () => void = () => console.log('🎨 Show color modal')
  let addImage: () => void = () => console.log('🖼️ Add image')

  // Register editor-specific keybinds
  const registerEditorKeybinds = () => {
    console.log('✏️ Registering editor keybinds...')

    // Basic text formatting
    globalKeybindManager.register({
      key: 'ctrl+b',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleBold().run()
        }
      },
      description: 'Toggle bold',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+i',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleItalic().run()
        }
      },
      description: 'Toggle italic',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+u',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleUnderline().run()
        }
      },
      description: 'Toggle underline',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+s',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleStrike().run()
        }
      },
      description: 'Toggle strikethrough',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    // Links and media
    globalKeybindManager.register({
      key: 'ctrl+k',
      handler: (context) => {
        if (context.editorFocused) {
          showLinkModal()
        }
      },
      description: 'Insert/edit link',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+k',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().unsetLink().run()
        }
      },
      description: 'Remove link',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    // History (undo/redo)
    globalKeybindManager.register({
      key: 'ctrl+z',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().undo().run()
        }
      },
      description: 'Undo',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+y',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().redo().run()
        }
      },
      description: 'Redo',
      category: KeybindCategory.EDITOR,
      priority: 'high',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+z',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().redo().run()
        }
      },
      description: 'Redo (alternative)',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    // Heading shortcuts
    globalKeybindManager.register({
      key: 'ctrl+alt+digit1',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleHeading({ level: 1 }).run()
        }
      },
      description: 'Toggle heading 1',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+alt+digit2',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleHeading({ level: 2 }).run()
        }
      },
      description: 'Toggle heading 2',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+alt+digit3',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleHeading({ level: 3 }).run()
        }
      },
      description: 'Toggle heading 3',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    // List shortcuts
    globalKeybindManager.register({
      key: 'ctrl+shift+digit7',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleOrderedList().run()
        }
      },
      description: 'Toggle ordered list',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+digit8',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleBulletList().run()
        }
      },
      description: 'Toggle bullet list',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+digit9',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleTaskList().run()
        }
      },
      description: 'Toggle task list',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    // Block formatting
    globalKeybindManager.register({
      key: 'ctrl+shift+.',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleBlockquote().run()
        }
      },
      description: 'Toggle blockquote',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+-',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().setHorizontalRule().run()
        }
      },
      description: 'Insert horizontal rule',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+shift+c',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleCodeBlock().run()
        }
      },
      description: 'Toggle code block',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    globalKeybindManager.register({
      key: 'ctrl+`',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleCode().run()
        }
      },
      description: 'Toggle inline code',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    // Text styling
    globalKeybindManager.register({
      key: 'ctrl+shift+h',
      handler: (context) => {
        if (context.editorFocused && editor.value) {
          editor.value.chain().focus().toggleHighlight().run()
        }
      },
      description: 'Toggle highlight',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    // Media insertion
    globalKeybindManager.register({
      key: 'ctrl+shift+p',
      handler: (context) => {
        if (context.editorFocused) {
          addImage()
        }
      },
      description: 'Insert image',
      category: KeybindCategory.EDITOR,
      priority: 'medium',
      enabled: true,
      context: { editorFocused: true }
    })

    console.log('✅ Editor keybinds registered')
  }

  // Unregister editor keybinds
  const unregisterEditorKeybinds = () => {
    console.log('🗑️ Unregistering editor keybinds...')
    
    const keysToUnregister = [
      'ctrl+b', 'ctrl+i', 'ctrl+u', 'ctrl+shift+s',
      'ctrl+k', 'ctrl+shift+k',
      'ctrl+z', 'ctrl+y', 'ctrl+shift+z',
      'ctrl+alt+digit1', 'ctrl+alt+digit2', 'ctrl+alt+digit3',
      'ctrl+shift+digit7', 'ctrl+shift+digit8', 'ctrl+shift+digit9',
      'ctrl+shift+.', 'ctrl+shift+-', 'ctrl+shift+c', 'ctrl+`',
      'ctrl+shift+h', 'ctrl+shift+p'
    ]
    
    keysToUnregister.forEach(key => {
      globalKeybindManager.unregister(key)
    })
  }

  // Activate editor keybinds
  const activate = () => {
    if (!isActive.value) {
      registerEditorKeybinds()
      isActive.value = true
      console.log('🟢 Editor keybinds activated')
    }
  }

  // Deactivate editor keybinds
  const deactivate = () => {
    if (isActive.value) {
      unregisterEditorKeybinds()
      isActive.value = false
      console.log('🔴 Editor keybinds deactivated')
    }
  }

  // Setup editor and functions
  const setupEditor = (editorInstance: Editor) => {
    editor.value = editorInstance
    console.log('🔧 Editor instance configured')
  }

  const setupFunctions = (functions: {
    showLinkModal?: () => void
    showFontModal?: () => void
    showColorModal?: () => void
    addImage?: () => void
  }) => {
    if (functions.showLinkModal) showLinkModal = functions.showLinkModal
    if (functions.showFontModal) showFontModal = functions.showFontModal
    if (functions.showColorModal) showColorModal = functions.showColorModal
    if (functions.addImage) addImage = functions.addImage
    
    console.log('🔧 Editor functions configured')
  }

  // Utility functions for external use
  const applyFontFamily = (fontFamily: string) => {
    if (editor.value) {
      editor.value.chain().focus().setFontFamily(fontFamily).run()
    }
  }

  const applyTextColor = (color: string) => {
    if (editor.value) {
      editor.value.chain().focus().setColor(color).run()
    }
  }

  const removeTextColor = () => {
    if (editor.value) {
      editor.value.chain().focus().unsetColor().run()
    }
  }

  // Cleanup
  onBeforeUnmount(() => {
    deactivate()
  })

  return {
    isActive,
    activate,
    deactivate,
    setupEditor,
    setupFunctions,
    applyFontFamily,
    applyTextColor,
    removeTextColor,
    registerEditorKeybinds,
    unregisterEditorKeybinds
  }
}