<template>
    <aside class="sidebar" :class="{ 'sidebar--collapsed': collapsed }">
        <header class="sidebar-header">
            <div class="logo-container" @click="goToDashboard" role="button" tabindex="0">
                <img src="/logo.svg" class="logo" alt="Logo" />
                <h1 class="noti-text" :class="{ 'noti-text--hidden': collapsed }">NOTI</h1>
            </div>

            <!-- Menu icon is now always present, but its style changes based on collapsed state -->
            <div class="menu-icon-container" @click="toggleSidebar"
                :class="{ 'menu-icon-container--collapsed': collapsed }">
                <img src="/menu-icon.svg" class="menu-icon" alt="Menu" :class="{ 'menu-icon--flipped': collapsed }" />
            </div>
        </header>

        <nav class="navigation-list">
            <div class="navigation-items">
                <router-link to="/" class="nav-button" exact>
                    <img src="/icons/dashboard-icon.svg" class="nav-button__icon" alt="" />
                    <span class="nav-button__text" :class="{ 'nav-button__text--hidden': collapsed }">Dashboard</span>
                </router-link>

                <router-link to="/notes" class="nav-button">
                    <img src="/icons/notes-icon.svg" class="nav-button__icon" alt="" />
                    <span class="nav-button__text" :class="{ 'nav-button__text--hidden': collapsed }">Notes</span>
                </router-link>

                <router-link to="/books" class="nav-button">
                    <img src="/icons/books-icon.svg" class="nav-button__icon" alt="" />
                    <span class="nav-button__text" :class="{ 'nav-button__text--hidden': collapsed }">Books</span>
                </router-link>

                <router-link to="/folders" class="nav-button">
                    <img src="/icons/folders-icon.svg" class="nav-button__icon" alt="" />
                    <span class="nav-button__text" :class="{ 'nav-button__text--hidden': collapsed }">Folders</span>
                </router-link>

                <router-link to="/timer" class="nav-button">
                    <img src="/icons/timer-icon.svg" class="nav-button__icon" alt="" />
                    <span class="nav-button__text" :class="{ 'nav-button__text--hidden': collapsed }">Timer</span>
                </router-link>
            </div>

            <router-link to="/settings" class="nav-button settings-button">
                <img src="/icons/settings-icon.svg" class="nav-button__icon" alt="" />
                <span class="nav-button__text" :class="{ 'nav-button__text--hidden': collapsed }">Settings</span>
            </router-link>
        </nav>
    </aside>
</template>

<script lang="ts">
export default {
    name: 'SidebarNavigation',
    data() {
        return {
            collapsed: false,
        };
    },
    methods: {
        toggleSidebar() {
            console.log('🔄 Toggling sidebar from', this.collapsed ? 'collapsed' : 'expanded', 'to', this.collapsed ? 'expanded' : 'collapsed');
            console.log('📏 Current sidebar width:', this.collapsed ? '80px' : '240px');
            console.log('🎯 Target sidebar width:', this.collapsed ? '240px' : '80px');
            
            this.collapsed = !this.collapsed;
            
            console.log('✅ Sidebar state changed to:', this.collapsed ? 'collapsed' : 'expanded');
            
            // Log animation start
            console.log('🎬 Starting animation...');
            
            // Log when animation should complete
            setTimeout(() => {
                console.log('🏁 Animation should be complete now');
                console.log('📐 Final sidebar width should be:', this.collapsed ? '80px' : '240px');
            }, 300); // Match the CSS transition duration
        },
        goToDashboard() {
            console.log('🏠 Navigating to dashboard');
            this.$router.push('/');
        }
    },
            watch: {
        collapsed(newVal, oldVal) {
            console.log('👀 Collapsed state changed:', oldVal, '->', newVal);
            console.log('🎭 Enhanced animation features:');
            console.log('  📝 Text: Staggered fade + slide transforms');
            console.log('  🔄 Icon: Smooth rotation with spring effect');
            console.log('  📐 Buttons: Slower spring transitions');
            console.log('  ♿ Accessibility: Respects prefers-reduced-motion');
            console.log('  ⏱️ Timing: Slower, more relaxed staggered timing');
            console.log('🎯 Strategy: Width collapse + transforms for perfect positioning');
        }
    },
    mounted() {
        console.log('🚀 Sidebar component mounted');
        console.log('📊 Initial state - collapsed:', this.collapsed);
        console.log('📏 Initial width: 240px');
    }
};
</script>

<style scoped>
/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
    .sidebar,
    .sidebar *,
    .sidebar *::before,
    .sidebar *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        transition-delay: 0ms !important;
    }
}

.sidebar {
    background-color: var(--color-nav-bg);
    display: flex;
    /* Reduced width here */
    width: 240px;
    padding: 35px 0 22px;
    flex-direction: column;
    align-items: center; /* Center children horizontally */
    font-family:
        Montserrat,
        -apple-system,
        Roboto,
        Helvetica,
        sans-serif;
    color: var(--color-text-primary);
    height: 100vh;
    border-right: 1px solid var(--color-nav-border);
    /* Enhanced transition with subtle spring effect */
    transition: width 0.6s cubic-bezier(0.34, 1.56, 0.64, 1), 
                padding 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    z-index: 10000; /* Increased from 1000 to ensure sidebar is above all content */
    /* Prevent text selection in sidebar */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    /* Removed overflow: hidden to allow menu icon to display outside bounds */
}

.sidebar--collapsed {
    width: 80px;
}

.sidebar-header {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center; /* Center logo container by default */
    font-size: 20px;
    font-weight: 900;
    white-space: nowrap;
    padding: 0 20px;
    height: 40px;
    position: relative; /* For absolute positioning of the menu icon */
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center; /* Center logo and text */
    height: 100%;
    cursor: pointer; /* Add pointer cursor to indicate clickable */
    transition: opacity 0.2s ease;
    /* Ensure smooth gap transition */
    transition: opacity 0.2s ease, gap 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-container:hover {
    opacity: 0.8; /* Add hover effect */
}

.logo {
    aspect-ratio: 1;
    object-fit: contain;
    object-position: center;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.noti-text {
    align-self: center;
    margin: 0 0 0 8px; /* Left margin for gap with logo */
    font-size: inherit;
    font-weight: inherit;
    white-space: nowrap;
    overflow: hidden;
    /* Enhanced transitions with staggered timing and transforms */
    opacity: 1;
    transform: translateX(0);
    max-width: 200px; /* Enough space for the text */
    transition: opacity 0.4s cubic-bezier(0.34, 1.56, 0.64, 1), 
                transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
                max-width 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
                margin 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.noti-text--hidden {
    opacity: 0;
    transform: translateX(-8px);
    max-width: 0;
    margin: 0;
}

/* Menu Icon Styling */
.menu-icon-container {
    position: absolute;
    top: 50%;
    right: -15px;
    background-color: var(--color-bg-primary);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px var(--color-card-shadow);
    z-index: 10001; /* Increased to be above the sidebar */
    cursor: pointer;
    transform: translateY(-50%);
    /* Enhanced transition with subtle movement */
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                box-shadow 0.2s ease,
                background-color 0.2s ease;
}

.menu-icon-container:hover {
    box-shadow: 0 3px 8px var(--color-card-hover-shadow);
    background-color: var(--color-nav-item-hover);
}

.menu-icon-container:active {
    transform: translateY(-50%);
    transition-duration: 0.1s;
}

.menu-icon {
    aspect-ratio: 1;
    object-fit: contain;
    object-position: center;
    width: 20px;
    flex-shrink: 0;
    /* Enhanced icon rotation with subtle movement */
    transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.menu-icon--flipped {
    transform: rotate(180deg);
}

/* Always keep menu icon on the right edge */
.menu-icon-container.menu-icon-container--collapsed {
    position: absolute;
    top: 50%;
    right: -15px;
    transform: translateY(-50%);
}

.navigation-list {
    width: 90%; /* Slightly narrower than the sidebar */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* Center vertically */
    height: calc(100% - 80px); /* Account for header and padding */
    margin-top: 26px;
}

.navigation-items {
    display: flex;
    flex-direction: column;
    gap: 20px; /* Increased gap between items */
    width: 100%;
    align-items: center;
}

.nav-button {
    border-radius: 10px;
    background-color: var(--color-nav-bg);
    display: flex;
    min-height: 50px;
    width: 100%;
    align-items: center;
    gap: 0; /* Remove gap, handled by text margin */
    white-space: nowrap;
    text-align: center;
    border: none;
    cursor: pointer;
    font-family: inherit;
    color: inherit;
    text-decoration: none;
    
    /* Default state: centered for collapsed sidebar */
    justify-content: center;
    padding: 15px 0;
    
    /* Enhanced transitions with spring effect */
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1),
                transform 0.2s ease;
}

.sidebar:not(.sidebar--collapsed) .nav-button {
    justify-content: flex-start; /* Left align when not collapsed */
    padding: 15px 0 15px 25px;
}

/* Keep settings button centered even in expanded mode */
.sidebar:not(.sidebar--collapsed) .settings-button {
    justify-content: center;
    padding: 15px 0;
}

.nav-button:hover {
    background-color: var(--color-nav-item-hover);
    transform: translateY(-1px); /* Subtle lift effect */
}

.nav-button:active {
    transform: translateY(0);
    transition-duration: 0.1s;
}

.nav-button.router-link-active {
    background-color: var(--color-nav-item-active);
    color: var(--color-primary);
    font-weight: 600;
}

.nav-button__icon {
    aspect-ratio: 0.94;
    object-fit: contain;
    object-position: center;
    width: 15px;
    flex-shrink: 0;
}

.nav-button__text {
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    /* Enhanced transitions with staggered timing and transforms */
    opacity: 1;
    transform: translateX(0);
    max-width: 100px; /* Enough space for button text */
    margin-left: 8px; /* Gap between icon and text */
    transition: opacity 0.35s cubic-bezier(0.34, 1.56, 0.64, 1), 
                transform 0.55s cubic-bezier(0.34, 1.56, 0.64, 1) 0.08s,
                max-width 0.55s cubic-bezier(0.34, 1.56, 0.64, 1) 0.08s,
                margin-left 0.55s cubic-bezier(0.34, 1.56, 0.64, 1) 0.08s;
}

.nav-button__text--hidden {
    opacity: 0;
    transform: translateX(-6px);
    max-width: 0;
    margin-left: 0;
}

.settings-button {
    margin-top: auto;
    margin-bottom: 10px;
    width: 100%;
}

/* Add smooth transitions for any layout shifts */
* {
    box-sizing: border-box;
}

/* Ensure smooth font rendering during animations */
.sidebar * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
</style>