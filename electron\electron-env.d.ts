/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface Window {
  // expose in the `electron/preload/index.ts`
  ipcRenderer: import('electron').IpcRenderer;

  // Database API exposed in preload
  db: {
    // Notes API
    notes: {
      create: (note: {
        title: string;
        content?: string;
        html_content?: string;
        folder_id?: number | null;
        book_id?: number | null;
        type?: string;
        color?: string;
        order?: number;
      }) => Promise<any>;
      getAll: (options?: any) => Promise<any[]>;
      getById: (id: number) => Promise<any>;
      getByFolderId: (folderId: number) => Promise<any[]>;
      getByBookId: (bookId: number) => Promise<any[]>;
      update: (id: number, noteUpdates: {
        title?: string;
        content?: string;
        html_content?: string;
        folder_id?: number | null;
        book_id?: number | null;
        type?: string;
        color?: string;
        order?: number;
        last_viewed_at?: string;
      }) => Promise<any>;
      delete: (id: number) => Promise<{ success: boolean; id: number }>;
      search: (searchTerm: string) => Promise<any[]>;
    };

    // Folders API
    folders: {
      create: (folder: {
        name: string;
        parent_id?: number | null;
        color?: string;
        order?: number;
      }) => Promise<any>;
      getAll: () => Promise<any[]>;
      getById: (id: number) => Promise<any>;
      getChildren: (parentId: number | null) => Promise<any[]>;
      getHierarchy: () => Promise<any[]>;
      update: (id: number, folderUpdates: {
        name?: string;
        parent_id?: number | null;
        color?: string;
        order?: number;
      }) => Promise<any>;
      delete: (id: number, targetFolderId?: number) => Promise<{ success: boolean; id: number }>;
    };

    // Recent Items API
    recentItems: {
      addNote: (noteId: number) => Promise<{ id: number; noteId: number }>;
      addBook: (bookId: number) => Promise<{ id: number; bookId: number }>;
      getNotes: (limit?: number) => Promise<any[]>;
      getBooks: (limit?: number) => Promise<any[]>;
      getAll: (limit?: number) => Promise<any[]>;
      clearAll: () => Promise<{ success: boolean }>;
      delete: (id: number) => Promise<{ success: boolean; id: number }>;
    };

    // Settings API
    settings: {
      get: (key: string) => Promise<{ id: number; key: string; value: any; category: string; updated_at: string }>;
      getByCategory: (category: string) => Promise<any[]>;
      getAll: () => Promise<any[]>;
      set: (key: string, value: any, category?: string) => Promise<{ id: number; key: string; value: any; category: string }>;
      delete: (key: string) => Promise<{ success: boolean; key: string }>;
    };

    // Themes API
    themes: {
      getActive: () => Promise<{ id: number; theme_name: string; is_active: number; updated_at: string }>;
      getAll: () => Promise<any[]>;
      create: (themeName: string) => Promise<{ id: number; theme_name: string; is_active: number }>;
      setActive: (themeId: number) => Promise<{ id: number; theme_name: string; is_active: number; updated_at: string }>;
      delete: (themeId: number) => Promise<{ success: boolean; id: number }>;
    };

    // Timer API
    timer: {
      // Timer sessions
      start: (sessionType?: string) => Promise<{ id: number; start_time: string; session_type: string; is_completed: number }>;
      end: (sessionId: number) => Promise<any>;
      getSession: (sessionId: number) => Promise<any>;
      getSessionsByDateRange: (startDate: string, endDate: string) => Promise<any[]>;
      getTodaySessions: () => Promise<any[]>;
      getStatsByDateRange: (startDate: string, endDate: string) => Promise<{
        total_sessions: number;
        total_duration: number;
        work_sessions: number;
        work_duration: number;
        break_sessions: number;
        break_duration: number;
      }>;
      deleteSession: (sessionId: number) => Promise<{ success: boolean; id: number }>;

      // Timer settings
      getSettings: () => Promise<{
        id: number;
        work_duration: number;
        short_break_duration: number;
        long_break_duration: number;
        long_break_interval: number;
        auto_start_breaks: number;
        auto_start_work: number;
        updated_at: string;
      }>;
      updateSettings: (settingsUpdates: {
        work_duration?: number;
        short_break_duration?: number;
        long_break_duration?: number;
        long_break_interval?: number;
        auto_start_breaks?: boolean;
        auto_start_work?: boolean;
      }) => Promise<any>;
      resetSettings: () => Promise<any>;
    };
  };

  // Window control API exposed in preload
  windowControls: {
    minimize: () => Promise<boolean>;
    maximize: () => Promise<boolean>;
    close: () => Promise<boolean>;
  };
}