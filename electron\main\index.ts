import { app, BrowserWindow, shell, ipcMain, Menu, protocol } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import os from 'node:os'
import { initializeIpcHandlers } from './ipc-handlers'
import { initializeProtocolHandlers } from './protocol-handlers'
import timerApi from './api/timer-api'
import { closeDatabase } from './database/database'
import discordRpc from '../../public/discord-rpc-api'
import { syncAPI } from './api/sync-logic/sync-api'
import { getSetting } from './api/settings-api'

// Disable security warnings in development mode
// This is only for development convenience - warnings will not show in production builds
if (process.env.NODE_ENV !== 'production') {
  process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';
}

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// The built directory structure
//
// ├─┬ dist-electron
// │ ├─┬ main
// │ │ └── index.js    > Electron-Main
// │ └─┬ preload
// │   └── index.mjs   > Preload-Scripts
// ├─┬ dist
// │ └── index.html    > Electron-Renderer
//
process.env.APP_ROOT = path.join(__dirname, '../..')

export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron')
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist')
export const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST

// Disable GPU Acceleration for Windows 7
if (os.release().startsWith('6.1')) app.disableHardwareAcceleration()

// Set application name for Windows 10+ notifications
if (process.platform === 'win32') app.setAppUserModelId(app.getName())

if (!app.requestSingleInstanceLock()) {
  app.quit()
  process.exit(0)
}

let win: BrowserWindow | null = null
const preload = path.join(__dirname, '../preload/index.mjs')
const indexHtml = path.join(RENDERER_DIST, 'index.html')


// Setup window control handlers
function setupWindowControlHandlers() {
  // Handler for window:minimize
  ipcMain.handle('window:minimize', () => {
    if (win && !win.isDestroyed()) {
      win.minimize();
    }
    return true;
  });

  // Handler for window:maximize
  ipcMain.handle('window:maximize', () => {
    if (!win || win.isDestroyed()) return false;
    
    if (win.isMaximized()) {
      win.unmaximize();
      return false;
    } else {
      win.maximize();
      return true;
    }
  });

  // Handler for window:close
  ipcMain.handle('window:close', () => {
    if (win && !win.isDestroyed()) {
      win.close();
    }
    return true;
  });
}

async function createWindow() {  // Base window configuration  // Check if we're explicitly in development mode
  const isDev = process.env.NODE_ENV === 'development' || !!process.env.VITE_DEV_SERVER_URL;

  const windowOptions: Electron.BrowserWindowConstructorOptions = {
    title: 'Noti',
    icon: path.join(process.env.VITE_PUBLIC, 'favicon.ico'),
    width: 1200,
    height: 800,
    frame: false, // Remove the standard window frame completely
    webPreferences: {
      preload,
      // Always enable web security - we'll use custom protocol for local resources
      // Using noti-media:// protocol handles file access safely
      webSecurity: true,
      // Never allow running insecure content
      allowRunningInsecureContent: false,
      // Additional security settings
      contextIsolation: true,
      nodeIntegration: false,
    },
  };
  
  // Create the window with the appropriate options
  win = new BrowserWindow(windowOptions);

  if (VITE_DEV_SERVER_URL) { // #298
    win.loadURL(VITE_DEV_SERVER_URL)
    // Open devTool if the app is not packaged
    win.webContents.openDevTools()
  } else {
    win.loadFile(indexHtml)
  }

  // Test actively push message to the Electron-Renderer
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', new Date().toLocaleString())
  })

  // Make all links open with the browser, not with the application
  win.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith('https:')) shell.openExternal(url)
    return { action: 'deny' }
  })
  // win.webContents.on('will-navigate', (event, url) => { }) #344
}

// Initialize database and IPC handlers when app is ready
app.whenReady().then(async () => {
  try {    // Register custom protocol handlers (like noti-media://)
    initializeProtocolHandlers();
    
    // Setup window control handlers
    setupWindowControlHandlers();
    
    // Initialize database and IPC handlers
    await initializeIpcHandlers();
    console.log('Database and IPC handlers initialized successfully');
    
    // Check for sync on startup
    try {
      console.log('Checking for sync directory...');
      const syncDirectory = await getSetting('syncDirectory');
      const autoSyncEnabled = await getSetting('autoSyncEnabled');
      
      if (syncDirectory?.value && 
          typeof autoSyncEnabled?.value === 'boolean' && 
          autoSyncEnabled.value === true) {
        console.log(`Sync directory found: ${syncDirectory.value}`);
        console.log('Auto-sync is enabled, performing startup sync...');
        
        // Perform sync in the background to not block startup
        syncAPI.performSync(syncDirectory.value).then(result => {
          console.log('Startup sync completed:', result);
        }).catch(error => {
          console.error('Startup sync failed:', error);
          // Don't throw - sync failure shouldn't prevent app from starting
        });
      } else {
        console.log('No sync directory configured or auto-sync disabled');
      }
    } catch (error) {
      console.error('Failed to check sync settings:', error);
      // Don't throw - this shouldn't block app startup
    }
    
    // Set the application menu to null to remove the default menu
    Menu.setApplicationMenu(null);

    // Create the application window
    await createWindow();


    // Initialize Discord RPC if user has it enabled
    try {
      console.log('Checking Discord Rich Presence settings...');
      // We'll initialize Discord from the frontend when settings are loaded
      // This allows instant activation if the user has it enabled
    } catch (error) {
      console.error('Failed to check Discord Rich Presence settings:', error);
      // Don't throw - this shouldn't block app startup
    }
  } catch (error) {
    console.error('Failed to initialize application:', error);
    app.quit();
  }
});

app.on('window-all-closed', () => {
  win = null
  if (process.platform !== 'darwin') app.quit()
})

// Close database connection before app quits
app.on('before-quit', async (event) => {
  event.preventDefault()
  try {
    // End any active timer sessions before closing
    const activeSession = await timerApi.getActiveUserSession();
    if (activeSession) {
      await timerApi.endUserSession(activeSession.id);
      console.log('Active session ended before app quit');
    }


    // Shutdown Discord RPC
    try {
      await discordRpc.destroy();
      console.log('Discord RPC shutdown successfully');
    } catch (error) {
      console.error('Error shutting down Discord RPC:', error);
      // Don't fail the quit process for this
    }

    await closeDatabase()
    console.log('Database closed successfully before quit')
    app.exit(0)
  } catch (err) {
    console.error('Error during app cleanup before quit:', err)
    app.exit(1)
  }
})

app.on('second-instance', () => {
  if (win) {
    // Focus on the main window if the user tried to open another
    if (win.isMinimized()) win.restore()
    win.focus()
  }
})

app.on('activate', () => {
  const allWindows = BrowserWindow.getAllWindows()
  if (allWindows.length) {
    allWindows[0].focus()
  } else {
    createWindow()
  }
})

// New window example arg: new windows url
ipcMain.handle('open-win', (_, arg) => {
  const childWindow = new BrowserWindow({
    webPreferences: {
      preload,
      nodeIntegration: true,
      contextIsolation: false,
    },
  })

  if (process.env.VITE_DEV_SERVER_URL) {
    childWindow.loadURL(`${VITE_DEV_SERVER_URL}#${arg}`)
  } else {
    childWindow.loadFile(indexHtml, { hash: arg })
  }
})