import { ipc<PERSON>ender<PERSON> } from 'electron';
import type {
  Note,
  Folder,
  Book,
  BookSearchResult,
  BookWithNoteCount
  // TODO: Add unified sync types when implemented
  // UnifiedSyncConfig,
  // UnifiedSyncResult,
  // ImportConfig
} from '../../src/types/electron-api';

// Create a type-safe API layer to be exposed by index.ts
export const dbApi = {
  // Notes API
  notes: {
    // Create a new note
    create: (note: Note) => ipcRenderer.invoke('notes:create', note),

    // Create a new note for a specific book
    createForBook: (bookId: number, customTitle?: string, folderId?: number | null) => ipcRenderer.invoke('notes:createForBook', bookId, customTitle, folderId),

    // Get all notes with optional filters
    getAll: (options?: any) => ipcRenderer.invoke('notes:getAll', options),

    // Get a specific note by ID
    getById: (id: number) => ipcRenderer.invoke('notes:getById', id),

    // Update a note
    update: (id: number, note: Partial<Note>) => ipcRenderer.invoke('notes:update', id, note),

    // Delete a note
    delete: (id: number) => ipcRenderer.invoke('notes:delete', id),

    // Get notes by folder ID
    getByFolderId: (folderId: number) => ipcRenderer.invoke('notes:getByFolderId', folderId),    // Export a note to a specific format
    export: (id: number, format: string) => ipcRenderer.invoke('notes:export', id, format),

    // Export multiple items (notes and/or folders)
    exportMultiple: (
      items: Array<{ id: number; type: 'folder' | 'note'; name: string }>,
      format: string,
      options?: { includeSubfolders?: boolean; includeNotes?: boolean }
    ) => ipcRenderer.invoke('notes:exportMultiple', items, format, options),

    // Import note content
    import: (content: string, format: string, title: string) =>
      ipcRenderer.invoke('notes:import', content, format, title),

    // Link a note to a book
    linkToBook: (noteId: number, bookId: number | null) =>
      ipcRenderer.invoke('notes:linkToBook', noteId, bookId),

    // Auto-link notes to books in a folder
    autoLinkInFolder: (folderId: number) =>
      ipcRenderer.invoke('notes:autoLinkInFolder', folderId)
  },

  // Books API
  books: {
    // Create a new book
    create: (book: Partial<Book>, downloadCover?: boolean) =>
      ipcRenderer.invoke('books:create', book, downloadCover),

    // Get all books
    getAll: () => ipcRenderer.invoke('books:getAll'),

    // Get all books with note counts
    getAllWithNoteCounts: () => ipcRenderer.invoke('books:getAllWithNoteCounts'),

    // Get books with metadata (enhanced for frontend)
    getBooksWithMetadata: () => ipcRenderer.invoke('books:getBooksWithMetadata'),

    // Get a specific book by ID
    getById: (id: number) => ipcRenderer.invoke('books:getById', id),

    // Get book by ISBN
    getByIsbn: (isbn: string) => ipcRenderer.invoke('books:getByIsbn', isbn),

    // Get book by OpenLibrary ID
    getByOlid: (olid: string) => ipcRenderer.invoke('books:getByOlid', olid),

    // Update a book
    update: (id: number, book: Partial<Book>) =>
      ipcRenderer.invoke('books:update', id, book),

    // Delete a book
    delete: (id: number) => ipcRenderer.invoke('books:delete', id),

    // Get recent books
    getRecent: (days?: number) => ipcRenderer.invoke('books:getRecent', days),

    // Search books locally
    search: (searchTerm: string) => ipcRenderer.invoke('books:search', searchTerm),

    // Search books online via OpenLibrary
    searchOnline: (query: string, limit?: number) =>
      ipcRenderer.invoke('books:searchOnline', query, limit),

    // Search books both locally and online
    searchHybrid: (searchTerm: string, includeOnline?: boolean, onlineLimit?: number) =>
      ipcRenderer.invoke('books:searchHybrid', searchTerm, includeOnline, onlineLimit),

    // Get detailed book information from OpenLibrary
    getDetailsFromOpenLibrary: (olid: string) =>
      ipcRenderer.invoke('books:getDetailsFromOpenLibrary', olid),

    // Add book from OpenLibrary search result
    addFromOpenLibrary: (searchResult: BookSearchResult) =>
      ipcRenderer.invoke('books:addFromOpenLibrary', searchResult),

    // Download cover image
    downloadCover: (coverUrl: string, filename: string) =>
      ipcRenderer.invoke('books:downloadCover', coverUrl, filename),

    // Check and download missing covers
    checkAndDownloadMissingCovers: () =>
      ipcRenderer.invoke('books:checkAndDownloadMissingCovers'),

    // Ensure folders for all books
    ensureFoldersForAll: () =>
      ipcRenderer.invoke('books:ensureFoldersForAll'),

    // Get books without folders
    getBooksWithoutFolders: () =>
      ipcRenderer.invoke('books:getBooksWithoutFolders')
  },

  // Media API
  media: {
    // Save a new media file
    save: (noteId: number | null, fileData: number[], fileName: string, fileType: string) =>
      ipcRenderer.invoke('media:save', noteId, fileData, fileName, fileType),

    // Get media file by ID
    getById: (id: number) => ipcRenderer.invoke('media:getById', id),

    // Get all media files for a note
    getByNoteId: (noteId: number) => ipcRenderer.invoke('media:getByNoteId', noteId),

    // Delete a media file
    delete: (id: number) => ipcRenderer.invoke('media:delete', id),

    // Update a media file's note association
    updateNote: (id: number, noteId: number | null) =>
      ipcRenderer.invoke('media:updateNote', id, noteId),

    // Save book cover specifically
    saveBookCover: (bookId: number, coverData: Uint8Array | number[], fileName?: string) =>
      ipcRenderer.invoke('media:saveBookCover', bookId, coverData, fileName),

    // Get the media storage path
    getStoragePath: () => ipcRenderer.invoke('media:getStoragePath'),

    // Get media URL by file path
    getMediaUrl: (filePath) => ipcRenderer.invoke('media:getMediaUrl', filePath)
  },

  // Folders API
  folders: {
    // Create a new folder
    create: (folder: Folder) => ipcRenderer.invoke('folders:create', folder),

    // Get all folders
    getAll: () => ipcRenderer.invoke('folders:getAll'),

    // Get a specific folder by ID
    getById: (id: number) => ipcRenderer.invoke('folders:getById', id),

    // Update a folder
    update: (id: number, folder: Partial<Folder>) =>
      ipcRenderer.invoke('folders:update', id, folder),

    // Delete a folder
    delete: (id: number, targetFolderId?: number | null) =>
      ipcRenderer.invoke('folders:delete', id, targetFolderId),

    // Get child folders of a parent
    getChildren: (parentId: number | null) =>
      ipcRenderer.invoke('folders:getChildren', parentId),

    // Get folder hierarchy for tree view
    getHierarchy: () => ipcRenderer.invoke('folders:getHierarchy'),

    // Get inherited book_id from parent hierarchy
    getInheritedBookId: (parentId: number | null) =>
      ipcRenderer.invoke('folders:getInheritedBookId', parentId)
  },

  // Recent Items API
  recentItems: {
    // Add a note to recent items
    addNote: (noteId: number) => ipcRenderer.invoke('recentItems:addNote', noteId),

    // Get recent notes
    getRecentNotes: (limit?: number) => ipcRenderer.invoke('recentItems:getRecentNotes', limit)
  },

  // Timer API
  timer: {
    // Timer sessions (legacy)
    start: (sessionType?: string, focus?: string, category?: string) =>
      ipcRenderer.invoke('timer:start', sessionType, focus, category),
    end: (sessionId: number) => ipcRenderer.invoke('timer:end', sessionId),
    getSession: (sessionId: number) => ipcRenderer.invoke('timer:getSession', sessionId),
    getSessionsByDateRange: (startDate: string, endDate: string) =>
      ipcRenderer.invoke('timer:getSessionsByDateRange', startDate, endDate),
    getTodaySessions: () => ipcRenderer.invoke('timer:getTodaySessions'),
    getStatsByDateRange: (startDate: string, endDate: string) =>
      ipcRenderer.invoke('timer:getStatsByDateRange', startDate, endDate),
    deleteSession: (sessionId: number) => ipcRenderer.invoke('timer:deleteSession', sessionId),
    updateSession: (sessionId: number, updateData: any) => ipcRenderer.invoke('timer:updateSession', sessionId, updateData),

    // New session management
    createUserSession: (sessionName: string, focus?: string, category?: string) =>
      ipcRenderer.invoke('timer:createUserSession', sessionName, focus, category),
    endUserSession: (sessionId: number) => ipcRenderer.invoke('timer:endUserSession', sessionId),
    getActiveUserSession: () => ipcRenderer.invoke('timer:getActiveUserSession'),

    // Pomodoro cycle management
    startPomodoroInSession: (sessionId: number, cycleType: string) =>
      ipcRenderer.invoke('timer:startPomodoroInSession', sessionId, cycleType),
    completePomodoroInSession: (sessionId: number, cycleId: number) =>
      ipcRenderer.invoke('timer:completePomodoroInSession', sessionId, cycleId),
    cancelActiveCycle: (sessionId: number, cycleId: number) =>
      ipcRenderer.invoke('timer:cancelActiveCycle', sessionId, cycleId),

    // Timer settings
    getSettings: () => ipcRenderer.invoke('timer:getSettings'),
    updateSettings: (settingsUpdates: any) => ipcRenderer.invoke('timer:updateSettings', settingsUpdates),
    resetSettings: () => ipcRenderer.invoke('timer:resetSettings'),

    // Utility functions
    syncAllSessionPomodoroCounts: () => ipcRenderer.invoke('timer:syncAllSessionPomodoroCounts')
  },

  // TODO: Add unified sync API when implemented
  // unifiedSync: {
  //   // Perform sync operation
  //   perform: (config: UnifiedSyncConfig) => ipcRenderer.invoke('unifiedSync:perform', config),
  //   
  //   // Get sync status
  //   getStatus: () => ipcRenderer.invoke('unifiedSync:getStatus'),
  //   
  //   // Update sync configuration
  //   updateConfig: (config: Partial<UnifiedSyncConfig>) => ipcRenderer.invoke('unifiedSync:updateConfig', config),
  //   
  //   // Import from backup directory
  //   importBackup: (directory: string, config: ImportConfig) => ipcRenderer.invoke('unifiedSync:importBackup', directory, config),
  //   
  //   // Validate sync directory
  //   validateLocation: (path: string) => ipcRenderer.invoke('unifiedSync:validateLocation', path)
  // },

  // Discord Rich Presence API
  discord: {
    // Initialize Discord RPC
    initialize: () => ipcRenderer.invoke('discord:initialize'),

    // Set Discord enabled state
    setEnabled: (enabled: boolean) => ipcRenderer.invoke('discord:setEnabled', enabled),

    // Set activity
    setActivity: (activityData: any) => ipcRenderer.invoke('discord:setActivity', activityData),

    // Set active state
    setActiveState: () => ipcRenderer.invoke('discord:setActiveState'),

    // Set idle activity
    setIdle: () => ipcRenderer.invoke('discord:setIdle'),

    // Update settings
    updateSettings: (settings: any) => ipcRenderer.invoke('discord:updateSettings', settings),

    // Clear activity
    clearActivity: () => ipcRenderer.invoke('discord:clearActivity'),

    // Get Discord status
    getStatus: () => ipcRenderer.invoke('discord:getStatus'),

    // Destroy Discord RPC
    destroy: () => ipcRenderer.invoke('discord:destroy'),

    // Test Discord connection
    testConnection: () => ipcRenderer.invoke('discord:testConnection')
  },

  // Settings API
  settings: {
    // Get a setting by key
    get: (key: string) => ipcRenderer.invoke('settings:get', key),

    // Get settings by category
    getByCategory: (category: string) => ipcRenderer.invoke('settings:getByCategory', category),

    // Get all settings
    getAll: () => ipcRenderer.invoke('settings:getAll'),

    // Set a setting
    set: (key: string, value: any, category?: string) => ipcRenderer.invoke('settings:set', key, value, category),

    // Delete a setting
    delete: (key: string) => ipcRenderer.invoke('settings:delete', key),

    // Theme settings
    getActiveTheme: () => ipcRenderer.invoke('themes:getActive'),
    getAllThemes: () => ipcRenderer.invoke('themes:getAll'),
    createTheme: (themeName: string) => ipcRenderer.invoke('themes:create', themeName),
    setActiveTheme: (themeId: number) => ipcRenderer.invoke('themes:setActive', themeId),
    deleteTheme: (themeId: number) => ipcRenderer.invoke('themes:delete', themeId)
  },

  // Sync API
  sync: {
    // Perform sync with directory
    perform: (directory: string) => ipcRenderer.invoke('sync:perform', directory),

    // Import backup from directory
    import: (directory: string) => ipcRenderer.invoke('sync:import', directory),

    // Get sync status
    getStatus: () => ipcRenderer.invoke('sync:getStatus'),

    // Configure sync settings
    configure: (settings: any) => ipcRenderer.invoke('sync:configure', settings),

    // Browse for sync directory
    browseDirectory: () => ipcRenderer.invoke('sync:browseDirectory')
  },

  // Dialog API for folder selection
  selectFolder: () => ipcRenderer.invoke('dialog:selectFolder')
};
