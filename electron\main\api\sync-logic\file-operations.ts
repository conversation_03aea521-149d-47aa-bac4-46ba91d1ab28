import { promises as fs } from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { 
  SyncNote, 
  SyncBookMeta, 
  SyncFolderStructure,
  SyncError,
  ErrorCode
} from './types';

export class FileOperations {
  private syncDirectory: string | null = null;

  /**
   * Set the sync directory for path validation
   * Must be called before any file operations in sync context
   */
  setSyncDirectory(directory: string): void {
    this.syncDirectory = path.resolve(directory);
  }

  /**
   * Get the current sync directory
   * @returns The current sync directory path or null if not set
   */
  getSyncDirectory(): string | null {
    return this.syncDirectory;
  }

  /**
   * Clear the sync directory (for cleanup)
   */
  clearSyncDirectory(): void {
    this.syncDirectory = null;
  }

  /**
   * Validate and sanitize a path to prevent directory traversal attacks
   * @param inputPath - The path to validate
   * @param baseDir - The base directory that paths must be within
   * @returns The resolved, validated path
   * @throws SyncError if path traversal is detected
   */
  private validatePath(inputPath: string, baseDir: string): string {
    // Normalize and resolve the paths
    const normalizedInput = path.normalize(inputPath);
    const resolvedPath = path.resolve(baseDir, normalizedInput);
    const resolvedBase = path.resolve(baseDir);
    
    // Ensure the resolved path starts with the base directory
    if (!resolvedPath.startsWith(resolvedBase)) {
      throw new SyncError(
        ErrorCode.PATH_ERROR, 
        'Path traversal attempt detected: Access outside sync directory is not allowed'
      );
    }
    
    // Additional validation to prevent special characters that could be problematic
    if (normalizedInput.includes('..') || normalizedInput.includes('~')) {
      throw new SyncError(
        ErrorCode.PATH_ERROR,
        'Invalid path characters detected'
      );
    }
    
    return resolvedPath;
  }

  /**
   * Read a note file and its associated metadata
   */
  async readNote(notePath: string): Promise<{ content: string; metadata: any }> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(notePath, this.syncDirectory)
        : path.normalize(notePath);
      
      // Read the .md file content
      const content = await this.readFileAtomic(validatedPath);
      
      // Construct metadata path (.noti.json file)
      const dir = path.dirname(validatedPath);
      const basename = path.basename(validatedPath, '.md');
      const metadataPath = path.join(dir, `${basename}.noti.json`);
      
      let metadata = {};
      
      // Try to read metadata file if it exists
      if (await this.exists(metadataPath)) {
        try {
          const metadataContent = await this.readFileAtomic(metadataPath);
          metadata = JSON.parse(metadataContent);
        } catch (error) {
          // If metadata file is corrupted, continue with empty metadata
          console.warn(`Failed to parse metadata for ${notePath}:`, error);
        }
      }
      
      return { content, metadata };
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_READ_ERROR,
        `Failed to read note at ${notePath}: ${(error as Error).message}`
      );
    }
  }

  /**
   * Write a note file and its metadata atomically
   */
  async writeNote(notePath: string, content: string, metadata: any): Promise<void> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(notePath, this.syncDirectory)
        : path.normalize(notePath);
      
      const dir = path.dirname(validatedPath);
      
      // Ensure directory exists
      await this.ensurePath(dir);
      
      // Write content file atomically
      await this.writeFileAtomic(validatedPath, content);
      
      // Metadata is now stored in the manifest instead of separate files
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_WRITE_ERROR,
        `Failed to write note at ${notePath}: ${(error as Error).message}`
      );
    }
  }

  /**
   * Ensure a directory path exists, creating it if necessary
   */
  async ensurePath(dirPath: string): Promise<void> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(dirPath, this.syncDirectory)
        : path.normalize(dirPath);
      await fs.mkdir(validatedPath, { recursive: true });
    } catch (error) {
      throw new SyncError(
        ErrorCode.PATH_ERROR,
        `Failed to ensure path ${dirPath}: ${(error as Error).message}`
      );
    }
  }

  /**
   * Read book metadata from .book-meta.json file
   */
  async readBookMeta(bookPath: string): Promise<SyncBookMeta> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(bookPath, this.syncDirectory)
        : path.normalize(bookPath);
      const metaPath = path.join(validatedPath, '.book-meta.json');
      
      if (!await this.exists(metaPath)) {
        throw new Error('Book metadata file not found');
      }
      
      const content = await this.readFileAtomic(metaPath);
      return JSON.parse(content) as SyncBookMeta;
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_READ_ERROR,
        `Failed to read book metadata at ${bookPath}: ${(error as Error).message}`
      );
    }
  }

  /**
   * Ensure book directory exists (metadata now stored in manifest)
   */
  async ensureBookDirectory(bookPath: string): Promise<void> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(bookPath, this.syncDirectory)
        : path.normalize(bookPath);
      
      // Ensure book directory exists
      await this.ensurePath(validatedPath);
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_WRITE_ERROR,
        `Failed to create book directory at ${bookPath}: ${(error as Error).message}`
      );
    }
  }

  /**
   * List directory contents recursively
   */
  async listDirectoryContents(dirPath: string): Promise<SyncFolderStructure> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(dirPath, this.syncDirectory)
        : path.normalize(dirPath);
      
      const result: SyncFolderStructure = {
        path: validatedPath,
        name: path.basename(validatedPath),
        type: 'folder',
        children: []
      };
      
      if (!await this.exists(validatedPath)) {
        return result;
      }
      
      const entries = await fs.readdir(validatedPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(validatedPath, entry.name);
        
        if (entry.isDirectory()) {
          // Recursively list subdirectory
          const subDir = await this.listDirectoryContents(fullPath);
          result.children.push(subDir);
        } else if (entry.isFile()) {
          // Add file to structure
          result.children.push({
            path: fullPath,
            name: entry.name,
            type: 'file'
          });
        }
      }
      
      return result;
    } catch (error) {
      throw new SyncError(
        ErrorCode.PATH_ERROR,
        `Failed to list directory contents at ${dirPath}: ${(error as Error).message}`
      );
    }
  }

  /**
   * Check if a file or directory exists
   */
  async exists(filePath: string): Promise<boolean> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(filePath, this.syncDirectory)
        : path.normalize(filePath);
      await fs.access(validatedPath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Calculate SHA-256 hash of content for change detection
   */
  calculateHash(content: string): string {
    return crypto
      .createHash('sha256')
      .update(content, 'utf8')
      .digest('hex');
  }

  /**
   * Read file atomically with error handling
   */
  async readFileAtomic(filePath: string): Promise<string> {
    try {
      // Validate path if sync directory is set
      const validatedPath = this.syncDirectory 
        ? this.validatePath(filePath, this.syncDirectory)
        : path.normalize(filePath);
      const content = await fs.readFile(validatedPath, 'utf8');
      return content;
    } catch (error) {
      const err = error as NodeJS.ErrnoException;
      if (err.code === 'ENOENT') {
        throw new Error(`File not found: ${filePath}`);
      } else if (err.code === 'EACCES') {
        throw new Error(`Permission denied: ${filePath}`);
      } else {
        throw new Error(`Failed to read file: ${err.message}`);
      }
    }
  }

  /**
   * Read a file as buffer without path validation - used for media files
   * @param filePath - Absolute path to the file to read
   * @returns The file content as a Buffer
   */
  async readFileBuffer(filePath: string): Promise<Buffer> {
    try {
      const content = await fs.readFile(filePath);
      return content;
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_READ_ERROR,
        `Failed to read file: ${(error as Error).message}`
      );
    }
  }

  /**
   * Write a buffer to file atomically
   */
  async writeFileBuffer(filePath: string, content: Buffer): Promise<void> {
    // Validate path if sync directory is set
    const validatedPath = this.syncDirectory
      ? this.validatePath(filePath, this.syncDirectory)
      : path.normalize(filePath);

    const dir = path.dirname(validatedPath);
    const basename = path.basename(validatedPath);
    const tempPath = path.join(dir, `.${basename}.tmp`);

    try {
      // Ensure directory exists
      await this.ensurePath(dir);

      // Write to temp file
      await fs.writeFile(tempPath, content);

      // Rename to final path (atomic on same filesystem)
      await fs.rename(tempPath, validatedPath);
    } catch (error) {
      // Clean up temp file if it exists
      try {
        await fs.unlink(tempPath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }

      throw new SyncError(
        ErrorCode.FILE_WRITE_ERROR,
        `Failed to write file: ${(error as Error).message}`
      );
    }
  }

  /**
   * Rename a file atomically using fs.rename()
   * @param oldPath - Current path of the file
   * @param newPath - New path for the file
   */
  async renameFile(oldPath: string, newPath: string): Promise<void> {
    // Validate paths if sync directory is set
    const validatedOldPath = this.syncDirectory
      ? this.validatePath(oldPath, this.syncDirectory)
      : path.normalize(oldPath);
    const validatedNewPath = this.syncDirectory
      ? this.validatePath(newPath, this.syncDirectory)
      : path.normalize(newPath);

    try {
      // Ensure the target directory exists
      const targetDir = path.dirname(validatedNewPath);
      await this.ensurePath(targetDir);

      // Perform atomic rename
      await fs.rename(validatedOldPath, validatedNewPath);

      console.log(`[FileOperations] Successfully renamed file: ${oldPath} -> ${newPath}`);
    } catch (error) {
      const err = error as NodeJS.ErrnoException;
      if (err.code === 'ENOENT') {
        throw new SyncError(
          ErrorCode.FILE_NOT_FOUND,
          `Source file not found: ${oldPath}`
        );
      } else if (err.code === 'EACCES') {
        throw new SyncError(
          ErrorCode.PERMISSION_DENIED,
          `Permission denied: Cannot rename ${oldPath} to ${newPath}`
        );
      } else if (err.code === 'EXDEV') {
        // Cross-filesystem rename - fall back to copy+delete
        console.warn(`[FileOperations] Cross-filesystem rename detected, falling back to copy+delete: ${oldPath} -> ${newPath}`);
        await this.copyAndDeleteFile(validatedOldPath, validatedNewPath);
      } else {
        throw new SyncError(
          ErrorCode.FILE_OPERATION_ERROR,
          `Failed to rename file: ${err.message}`
        );
      }
    }
  }

  /**
   * Rename a directory atomically using fs.rename()
   * @param oldPath - Current path of the directory
   * @param newPath - New path for the directory
   */
  async renameDirectory(oldPath: string, newPath: string): Promise<void> {
    // Validate paths if sync directory is set
    const validatedOldPath = this.syncDirectory
      ? this.validatePath(oldPath, this.syncDirectory)
      : path.normalize(oldPath);
    const validatedNewPath = this.syncDirectory
      ? this.validatePath(newPath, this.syncDirectory)
      : path.normalize(newPath);

    try {
      // Ensure the target parent directory exists
      const targetParentDir = path.dirname(validatedNewPath);
      await this.ensurePath(targetParentDir);

      // Perform atomic rename
      await fs.rename(validatedOldPath, validatedNewPath);

      console.log(`[FileOperations] Successfully renamed directory: ${oldPath} -> ${newPath}`);
    } catch (error) {
      const err = error as NodeJS.ErrnoException;
      if (err.code === 'ENOENT') {
        throw new SyncError(
          ErrorCode.FILE_NOT_FOUND,
          `Source directory not found: ${oldPath}`
        );
      } else if (err.code === 'EACCES') {
        throw new SyncError(
          ErrorCode.PERMISSION_DENIED,
          `Permission denied: Cannot rename ${oldPath} to ${newPath}`
        );
      } else if (err.code === 'EEXIST') {
        throw new SyncError(
          ErrorCode.FILE_ALREADY_EXISTS,
          `Target directory already exists: ${newPath}`
        );
      } else if (err.code === 'EXDEV') {
        // Cross-filesystem rename - fall back to copy+delete
        console.warn(`[FileOperations] Cross-filesystem rename detected, falling back to copy+delete: ${oldPath} -> ${newPath}`);
        await this.copyAndDeleteDirectory(validatedOldPath, validatedNewPath);
      } else {
        throw new SyncError(
          ErrorCode.FILE_OPERATION_ERROR,
          `Failed to rename directory: ${err.message}`
        );
      }
    }
  }

  /**
   * Fallback method for cross-filesystem file rename (copy + delete)
   */
  private async copyAndDeleteFile(oldPath: string, newPath: string): Promise<void> {
    try {
      // Read source file
      const content = await fs.readFile(oldPath);

      // Write to new location
      await fs.writeFile(newPath, content);

      // Delete original file
      await fs.unlink(oldPath);

      console.log(`[FileOperations] Cross-filesystem file move completed: ${oldPath} -> ${newPath}`);
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_OPERATION_ERROR,
        `Failed to copy and delete file: ${(error as Error).message}`
      );
    }
  }

  /**
   * Fallback method for cross-filesystem directory rename (copy + delete)
   */
  private async copyAndDeleteDirectory(oldPath: string, newPath: string): Promise<void> {
    try {
      // Use recursive copy and delete
      await this.copyDirectoryRecursive(oldPath, newPath);
      await fs.rm(oldPath, { recursive: true, force: true });

      console.log(`[FileOperations] Cross-filesystem directory move completed: ${oldPath} -> ${newPath}`);
    } catch (error) {
      throw new SyncError(
        ErrorCode.FILE_OPERATION_ERROR,
        `Failed to copy and delete directory: ${(error as Error).message}`
      );
    }
  }

  /**
   * Recursively copy a directory
   */
  private async copyDirectoryRecursive(srcDir: string, destDir: string): Promise<void> {
    await this.ensurePath(destDir);

    const entries = await fs.readdir(srcDir, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(srcDir, entry.name);
      const destPath = path.join(destDir, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectoryRecursive(srcPath, destPath);
      } else {
        const content = await fs.readFile(srcPath);
        await fs.writeFile(destPath, content);
      }
    }
  }

  /**
   * Write file atomically using temp file and rename
   */
  async writeFileAtomic(filePath: string, content: string): Promise<void> {
    // Validate path if sync directory is set
    const validatedPath = this.syncDirectory 
      ? this.validatePath(filePath, this.syncDirectory)
      : path.normalize(filePath);
    
    const dir = path.dirname(validatedPath);
    const basename = path.basename(validatedPath);
    const tempPath = path.join(dir, `.${basename}.tmp`);
    
    try {
      // Write to temp file first
      await fs.writeFile(tempPath, content, 'utf8');
      
      // Atomically rename temp file to target
      await fs.rename(tempPath, validatedPath);
    } catch (error) {
      // Clean up temp file if it exists
      try {
        await fs.unlink(tempPath);
      } catch {
        // Ignore cleanup errors
      }
      
      const err = error as NodeJS.ErrnoException;
      if (err.code === 'EACCES') {
        throw new Error(`Permission denied: ${filePath}`);
      } else if (err.code === 'ENOSPC') {
        throw new Error(`No space left on device: ${filePath}`);
      } else {
        throw new Error(`Failed to write file: ${err.message}`);
      }
    }
  }
}

// Export singleton instance
export const fileOperations = new FileOperations();