# PowerShell script to show codebase structure
param(
    [string]$Path = ".",
    [string[]]$Exclude = @('node_modules', '.git', 'BugFixesMD', 'Code Documentation', 'public', '.vscode', 'dist', 'build', '.next', 'coverage', '.nyc_output', 'logs', 'tmp', 'temp')
)

function Show-DirectoryTree {
    param(
        [string]$CurrentPath,
        [int]$Indent = 0,
        [string[]]$ExcludeList
    )
    
    $items = Get-ChildItem -Path $CurrentPath | Where-Object { $_.Name -notin $ExcludeList } | Sort-Object { $_.PSIsContainer }, Name
    
    for ($i = 0; $i -lt $items.Count; $i++) {
        $item = $items[$i]
        $isLast = ($i -eq $items.Count - 1)
        
        $prefix = "  " * $Indent
        if ($isLast) {
            $prefix += "|-- "
        } else {
            $prefix += "|-- "
        }
        
        if ($item.PSIsContainer) {
            Write-Host ($prefix + $item.Name + "/") -ForegroundColor Cyan
            Show-DirectoryTree -CurrentPath $item.FullName -Indent ($Indent + 1) -ExcludeList $ExcludeList
        } else {
            Write-Host ($prefix + $item.Name) -ForegroundColor White
        }
    }
}

Write-Host "Noti/" -ForegroundColor Yellow
Show-DirectoryTree -CurrentPath $Path -ExcludeList $Exclude