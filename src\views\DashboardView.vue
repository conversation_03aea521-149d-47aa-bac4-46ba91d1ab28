<template>
  <div class="dashboard-view">
    <!-- Header Section -->
    <div class="dashboard-header">
      <h1 class="greeting">{{ greeting }}</h1>
      <p class="date">{{ currentDate }}</p>
    </div>

    <!-- Statistics Cards -->
    <DashboardStats />

    <!-- Quick Actions -->
    <QuickActions />

    <!-- Recent Activity -->
    <RecentActivity />

    <!-- Compact Charts -->
    <div class="dashboard-charts">
      <WeeklyActivityChart />
      <FocusTrendChart />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import DashboardStats from '../components/dashboard/DashboardStats.vue'
import QuickActions from '../components/dashboard/QuickActions.vue'
import RecentActivity from '../components/dashboard/RecentActivity.vue'
import WeeklyActivityChart from '../components/dashboard/charts/WeeklyActivityChart.vue'
import FocusTrendChart from '../components/dashboard/charts/FocusTrendChart.vue'
import { useDiscordActivity } from '../composables/useDiscordActivity'
import { useSettingsStore } from '../stores/settingsStore'

const { setDashboardActivity } = useDiscordActivity()
const settingsStore = useSettingsStore()

// Computed properties
const greeting = computed(() => {
  const hour = new Date().getHours()
  const userName = settingsStore.settings.userName.trim()

  let timeGreeting = ''
  if (hour < 12) timeGreeting = 'Good morning'
  else if (hour < 17) timeGreeting = 'Good afternoon'
  else timeGreeting = 'Good evening'

  return userName ? `${timeGreeting}, ${userName}!` : `${timeGreeting}!`
})

const currentDate = computed(() => {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
})

// Set Discord activity when user enters dashboard
onMounted(() => {
  setDashboardActivity()
})
</script>

<style scoped>
.dashboard-view {
  padding: 32px;
  width: 100%;
  min-height: 100vh;
  background-color: var(--color-bg-primary);
  /* Prevent text selection in dashboard */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.dashboard-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border-secondary);
}

.greeting {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-text-primary);
  letter-spacing: -0.025em;
}

.date {
  color: var(--color-dashboard-date);
  font-size: 1rem;
  font-weight: 400;
  opacity: 0.8;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .dashboard-view {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard-view {
    padding: 16px;
  }

  .dashboard-header {
    margin-bottom: 24px;
    padding-bottom: 12px;
  }

  .greeting {
    font-size: 1.6rem;
    margin-bottom: 6px;
  }

  .date {
    font-size: 0.9rem;
  }

  .dashboard-charts {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .dashboard-view {
    padding: 12px;
  }

  .greeting {
    font-size: 1.4rem;
  }

  .date {
    font-size: 0.85rem;
  }
}
</style>