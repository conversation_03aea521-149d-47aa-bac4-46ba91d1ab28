<template>
    <div class="settings-view">
        <div class="settings-content">
            <UserSettings />
            <ThemeSettings />
            <DiscordSettings />
            <BackupSettings />
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import UserSettings from '../components/settings/UserSettings.vue'
import ThemeSettings from '../components/settings/ThemeSettings.vue'
import DiscordSettings from '../components/settings/DiscordSettings.vue'
import BackupSettings from '../components/settings/BackupSettings.vue'
import { useDiscordActivity } from '../composables/useDiscordActivity'

const { setSettingsActivity } = useDiscordActivity()

// Set Discord activity when user enters settings
onMounted(() => {
  setSettingsActivity()
})
</script>

<style scoped>
.settings-view {
    padding: 20px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}


.settings-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}
</style>