# Keybind System Integration Guide

This guide shows how to integrate the keybind system into each view and component of the Noti application.

## 🚀 Phase 1 Implementation Complete!

The following core keybind system has been implemented:

### ✅ Completed Files:
- `src/types/keybinds.ts` - Type definitions
- `src/utils/keybindUtils.ts` - Core utilities and KeybindManager
- `src/composables/useGlobalKeybinds.ts` - Global navigation shortcuts
- `src/composables/useNotesKeybinds.ts` - Notes view shortcuts
- `src/composables/useEditorKeybinds.ts` - Rich text editor shortcuts
- `src/composables/useTimerKeybinds.ts` - Timer view shortcuts
- `src/composables/useBooksKeybinds.ts` - Books view shortcuts
- `src/composables/useFoldersKeybinds.ts` - Folders view shortcuts
- `src/composables/useModalKeybinds.ts` - Modal shortcuts
- `src/App.vue` - Global keybind system integration

---

## 📋 Integration Instructions

### 1. NotesView Integration

Add to `src/views/NotesView.vue`:

```typescript
// Add import
import { useNotesKeybinds } from '../composables/useNotesKeybinds'

// In setup function
const { setupNoteFunctions, activate, deactivate } = useNotesKeybinds()

// Configure keybind functions
setupNoteFunctions({
  createNewNote,
  saveCurrentNote: () => saveNote(selectedNote.value),
  focusSearchBar: () => searchInputRef.value?.focus(),
  deleteSelectedNotes: handleTrashButtonClick,
  importNote,
  selectAllNotes: () => {
    selectedNotes.value = [...filteredNotes.value]
  },
  navigateNoteList: (direction) => {
    // Implement note list navigation
  },
  openSelectedNote: () => {
    if (selectedNotes.value.length === 1) {
      selectNote(selectedNotes.value[0])
    }
  }
})

// Activate when view becomes active
onMounted(() => activate())
onBeforeUnmount(() => deactivate())
```

### 2. NoteEditor Integration

Add to `src/components/notes/NoteEditor.vue`:

```typescript
// Add import
import { useEditorKeybinds } from '../../composables/useEditorKeybinds'

// In setup function
const { setupEditor, setupFunctions, activate, deactivate } = useEditorKeybinds()

// After editor is created
watch(editor, (newEditor) => {
  if (newEditor) {
    setupEditor(newEditor)
    setupFunctions({
      showLinkModal: () => linkModalVisible.value = true,
      showFontModal: () => emit('show-font-modal'),
      showColorModal: () => emit('show-color-modal'),
      addImage
    })
    activate()
  }
})

// Cleanup
onBeforeUnmount(() => deactivate())
```

### 3. TimerView Integration

Add to `src/views/TimerView.vue`:

```typescript
// Add import
import { useTimerKeybinds } from '../composables/useTimerKeybinds'

// In setup function
const { setupTimerFunctions, activate, deactivate } = useTimerKeybinds()

// Configure keybind functions
setupTimerFunctions({
  toggleTimer: () => {
    // Call existing timer toggle logic
  },
  resetTimer: () => {
    // Call existing timer reset logic
  },
  skipTimer: () => {
    // Call existing timer skip logic
  },
  switchToPomodoro: () => {
    // Switch to pomodoro mode
  },
  switchToShortBreak: () => {
    // Switch to short break mode
  },
  switchToLongBreak: () => {
    // Switch to long break mode
  },
  createNewSession: () => {
    showAddSessionModal.value = true
  },
  endCurrentSession: () => {
    endSession()
  },
  openTimerSettings: () => {
    // Open timer settings modal
  }
})

// Activate when view becomes active
onMounted(() => activate())
onBeforeUnmount(() => deactivate())
```

### 4. BooksView Integration

Add to `src/views/BooksView.vue`:

```typescript
// Add import
import { useBooksKeybinds } from '../composables/useBooksKeybinds'

// In setup function
const { setupBookFunctions, activate, deactivate } = useBooksKeybinds()

// Configure keybind functions
setupBookFunctions({
  openAddBookModal: () => {
    showAddBookModal.value = true
  },
  focusBookSearch: () => {
    // Focus the book search input
  },
  openBookDetails: () => {
    // Open selected book details
  },
  deleteSelectedBook: () => {
    // Delete selected book
  },
  openBookNote: () => {
    // Open note associated with selected book
  },
  createBookNote: () => {
    // Create new note for selected book
  }
})

// Activate when view becomes active
onMounted(() => activate())
onBeforeUnmount(() => deactivate())
```

### 5. FoldersView Integration

Add to `src/views/FoldersView.vue`:

```typescript
// Add import
import { useFoldersKeybinds } from '../composables/useFoldersKeybinds'

// In setup function
const { setupFolderFunctions, activate, deactivate } = useFoldersKeybinds()

// Configure keybind functions
setupFolderFunctions({
  createNewFolder: createNewFolderInCurrent,
  renameSelectedFolder: () => {
    // Implement rename functionality
  },
  deleteSelectedFolders: () => handleDeleteSelectedFolders(navigatorSelectedItems),
  enterSelectedFolder: () => {
    // Enter selected folder
  },
  navigateUp: () => {
    // Navigate to parent folder
  },
  goToRoot: () => {
    // Navigate to root folder
  },
  focusFolderSearch: () => {
    searchInputRef.value?.focus()
  }
})

// Activate when view becomes active
onMounted(() => activate())
onBeforeUnmount(() => deactivate())
```

### 6. Modal Integration

For any modal component, add:

```typescript
// Add import
import { useModalKeybinds } from '../composables/useModalKeybinds'

// In setup function
const { setupModalFunctions } = useModalKeybinds()

// Configure modal functions
setupModalFunctions({
  confirmModal: () => {
    // Handle modal confirmation (save, confirm, etc.)
  },
  cancelModal: () => {
    emit('close')
  }
})
```

---

## 🎹 Available Shortcuts

### Global Navigation (Always Active)
- `Ctrl + 1-6`: Switch between views
- `Ctrl + \``: Toggle sidebar
- `Escape`: Close active modal
- `F11`: Toggle fullscreen

### Notes View
- `Ctrl + N`: Create new note
- `Ctrl + S`: Save current note
- `Ctrl + F`: Focus search bar
- `Delete`: Delete selected notes
- `Ctrl + I`: Import note
- `Ctrl + A`: Select all notes
- `↑/↓`: Navigate note list
- `Enter`: Open selected note

### Rich Text Editor
- `Ctrl + B/I/U`: Bold/Italic/Underline
- `Ctrl + Shift + S`: Strikethrough
- `Ctrl + K`: Insert link
- `Ctrl + Z/Y`: Undo/Redo
- `Ctrl + Alt + 1-3`: Headings
- `Ctrl + Shift + 7-9`: Lists
- And many more...

### Timer View
- `Space`: Start/pause timer
- `R`: Reset timer
- `S`: Skip timer
- `1-3`: Switch timer modes
- `Ctrl + N`: New session
- `Ctrl + E`: End session

### Books View
- `Ctrl + N`: Add new book
- `Ctrl + F`: Focus search
- `Enter`: Open book details
- `Delete`: Delete book
- `Ctrl + O`: Open book note

### Folders View
- `Ctrl + N`: Create new folder
- `F2`: Rename folder
- `Delete`: Delete folders
- `Enter`: Enter folder
- `Backspace`: Navigate up
- `Ctrl + Home`: Go to root

---

## 🔧 Customization

### To Remove Specific Shortcuts:
1. Comment out the keybind registration in the relevant composable
2. Example: In `useNotesKeybinds.ts`, comment out the import shortcut:
```typescript
// globalKeybindManager.register({
//   key: 'ctrl+i',
//   handler: (context) => {
//     if (context.view === 'notes' && !context.modalOpen && !context.editorFocused) {
//       importNote()
//     }
//   },
//   // ... rest of config
// })
```

### To Add New Shortcuts:
1. Add a new registration in the appropriate composable
2. Follow the existing pattern for context checking
3. Ensure the key combination doesn't conflict with existing shortcuts

---

## 🧪 Testing

1. **Test Global Navigation**: Try `Ctrl + 1-6` to switch between views
2. **Test View-Specific**: Enter each view and test its shortcuts
3. **Test Context Awareness**: Ensure shortcuts only work in appropriate contexts
4. **Test Modals**: Open modals and test `Enter`/`Escape`
5. **Test Editor**: Focus the note editor and test formatting shortcuts

---

## 🐛 Troubleshooting

### Shortcuts Not Working?
1. Check browser console for keybind registration logs
2. Verify the view-specific composable is activated
3. Ensure no browser extensions are intercepting shortcuts
4. Check if the context (modal open, editor focused) is correct

### Conflicts with Browser Shortcuts?
1. Some shortcuts like `Ctrl + R` might conflict with browser refresh
2. Consider using different key combinations
3. The system will prevent default behavior for registered shortcuts

### Debug Information:
```typescript
import { globalKeybindManager } from '../utils/keybindUtils'

// Get debug information
console.log(globalKeybindManager.getDebugInfo())
```

---

## 📝 Next Steps

1. **Integrate into Views**: Follow the integration instructions above
2. **Test Thoroughly**: Verify all shortcuts work as expected
3. **Customize**: Remove unwanted shortcuts and add new ones as needed
4. **Document**: Update user documentation with available shortcuts

The keybind system is now ready for integration and testing! 🎉