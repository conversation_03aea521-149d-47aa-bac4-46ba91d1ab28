import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
// Import the fonts CSS first to ensure font availability
import './assets/fonts.css'
// Import theme system
import './assets/themes.css'
// Import mock API - will only activate when real Electron API is not available
import './types/mock-api'

// Create Pinia instance
const pinia = createPinia()

// Create and mount the app with router and pinia
const app = createApp(App)
app.use(router)
app.use(pinia)

app.mount('#app')